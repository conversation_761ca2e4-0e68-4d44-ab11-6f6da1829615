{"version": 2, "dgSpecHash": "g41glfu2l5ZcERDhMLtaPd40wCwKwjVayM5HMFrrqIZyYuPa1JeN0G9RDfL8l70+UUGu3AEkI919rJcrxwdPLg==", "success": true, "projectFilePath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\OctopiInstaller\\SimpleInstaller\\OctopiInstaller.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.win-x64\\6.0.36\\microsoft.netcore.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.runtime.win-x64\\6.0.36\\microsoft.windowsdesktop.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.win-x64\\6.0.36\\microsoft.aspnetcore.app.runtime.win-x64.6.0.36.nupkg.sha512"], "logs": []}