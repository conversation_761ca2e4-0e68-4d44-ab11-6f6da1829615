﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "48B2617EE9A08530480BD870E74DD9093755283F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using ProjectManager.WPF;
using ProjectManager.WPF.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ProjectManager.WPF {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSettings;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl mainTabControl;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddProject;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditProject;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteProject;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSelectAll;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSelectNone;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnImportCSV;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExportCSV;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid projectsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbTeamFilter;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbEquipmentFilter;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnApplyFilters;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClearFilters;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ProjectManager.WPF.Controls.GanttChart ganttChart;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnProjectSummary;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTeamWorkload;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEquipmentBreakdown;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExportPDF;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl reportsTabControl;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid summaryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl settingsTabControl;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddTeam;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditTeam;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteTeam;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid teamsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkDarkMode;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoSave;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkShowNotifications;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbDefaultEquipmentType;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbDefaultProjectStatus;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveSettings;
        
        #line default
        #line hidden
        
        
        #line 435 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusText;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock projectCountText;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock teamCountText;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lastUpdatedText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ProjectManager.WPF;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 50 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MenuAbout_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\MainWindow.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 97 "..\..\..\MainWindow.xaml"
            this.btnSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSettings_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.mainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 5:
            this.btnAddProject = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\MainWindow.xaml"
            this.btnAddProject.Click += new System.Windows.RoutedEventHandler(this.BtnAddProject_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.btnEditProject = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\MainWindow.xaml"
            this.btnEditProject.Click += new System.Windows.RoutedEventHandler(this.BtnEditProject_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnDeleteProject = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\MainWindow.xaml"
            this.btnDeleteProject.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteProject_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnSelectAll = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\MainWindow.xaml"
            this.btnSelectAll.Click += new System.Windows.RoutedEventHandler(this.BtnSelectAll_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnSelectNone = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\MainWindow.xaml"
            this.btnSelectNone.Click += new System.Windows.RoutedEventHandler(this.BtnSelectNone_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnImportCSV = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\MainWindow.xaml"
            this.btnImportCSV.Click += new System.Windows.RoutedEventHandler(this.BtnImportCSV_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnExportCSV = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\MainWindow.xaml"
            this.btnExportCSV.Click += new System.Windows.RoutedEventHandler(this.BtnExportCSV_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.projectsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 189 "..\..\..\MainWindow.xaml"
            this.projectsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProjectsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 190 "..\..\..\MainWindow.xaml"
            this.projectsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.ProjectsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 13:
            this.cmbTeamFilter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.cmbEquipmentFilter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.btnApplyFilters = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\..\MainWindow.xaml"
            this.btnApplyFilters.Click += new System.Windows.RoutedEventHandler(this.BtnApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnClearFilters = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\MainWindow.xaml"
            this.btnClearFilters.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ganttChart = ((ProjectManager.WPF.Controls.GanttChart)(target));
            return;
            case 18:
            this.btnProjectSummary = ((System.Windows.Controls.Button)(target));
            
            #line 274 "..\..\..\MainWindow.xaml"
            this.btnProjectSummary.Click += new System.Windows.RoutedEventHandler(this.BtnProjectSummary_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btnTeamWorkload = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\MainWindow.xaml"
            this.btnTeamWorkload.Click += new System.Windows.RoutedEventHandler(this.BtnTeamWorkload_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btnEquipmentBreakdown = ((System.Windows.Controls.Button)(target));
            
            #line 288 "..\..\..\MainWindow.xaml"
            this.btnEquipmentBreakdown.Click += new System.Windows.RoutedEventHandler(this.BtnEquipmentBreakdown_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btnExportPDF = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\MainWindow.xaml"
            this.btnExportPDF.Click += new System.Windows.RoutedEventHandler(this.BtnExportPDF_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.btnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 303 "..\..\..\MainWindow.xaml"
            this.btnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.reportsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 24:
            this.summaryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 25:
            this.settingsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 26:
            this.btnAddTeam = ((System.Windows.Controls.Button)(target));
            
            #line 359 "..\..\..\MainWindow.xaml"
            this.btnAddTeam.Click += new System.Windows.RoutedEventHandler(this.BtnAddTeam_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.btnEditTeam = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\MainWindow.xaml"
            this.btnEditTeam.Click += new System.Windows.RoutedEventHandler(this.BtnEditTeam_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.btnDeleteTeam = ((System.Windows.Controls.Button)(target));
            
            #line 373 "..\..\..\MainWindow.xaml"
            this.btnDeleteTeam.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteTeam_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.teamsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 30:
            this.chkDarkMode = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 31:
            this.chkAutoSave = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.chkShowNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.cmbDefaultEquipmentType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.cmbDefaultProjectStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 35:
            this.btnSaveSettings = ((System.Windows.Controls.Button)(target));
            
            #line 423 "..\..\..\MainWindow.xaml"
            this.btnSaveSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.statusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.projectCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.teamCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.lastUpdatedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

