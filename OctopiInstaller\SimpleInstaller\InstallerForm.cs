using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OctopiProjectManagerInstaller
{
    public partial class InstallerForm : Form
    {
        private Label headerLabel;
        private Label versionLabel;
        private Label descLabel;
        private Label devLabel;
        private Label pathLabel;
        private TextBox pathTextBox;
        private Button browseButton;
        private CheckBox desktopCheckBox;
        private CheckBox startMenuCheckBox;
        private ProgressBar progressBar;
        private Label statusLabel;
        private Button installButton;
        private Button cancelButton;

        public InstallerForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Octopi Project Manager Installer";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Header
            headerLabel = new Label
            {
                Text = "🐙 Octopi Project Manager",
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                Location = new Point(20, 20),
                Size = new Size(450, 30)
            };
            this.Controls.Add(headerLabel);

            // Version
            versionLabel = new Label
            {
                Text = "Version 1.0.0",
                Font = new Font("Arial", 10),
                Location = new Point(20, 50),
                Size = new Size(200, 20)
            };
            this.Controls.Add(versionLabel);

            // Description
            descLabel = new Label
            {
                Text = "Comprehensive project management solution designed for Octopi Smart Solutions",
                Font = new Font("Arial", 9),
                Location = new Point(20, 80),
                Size = new Size(450, 40)
            };
            this.Controls.Add(descLabel);

            // Developer
            devLabel = new Label
            {
                Text = "Developed by Conrad Cloete (AntmanZA)",
                Font = new Font("Arial", 9, FontStyle.Italic),
                Location = new Point(20, 120),
                Size = new Size(300, 20)
            };
            this.Controls.Add(devLabel);

            // Install path
            pathLabel = new Label
            {
                Text = "Installation Path:",
                Location = new Point(20, 160),
                Size = new Size(100, 20)
            };
            this.Controls.Add(pathLabel);

            pathTextBox = new TextBox
            {
                Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "OctopiProjectManager"),
                Location = new Point(20, 180),
                Size = new Size(350, 20)
            };
            this.Controls.Add(pathTextBox);

            browseButton = new Button
            {
                Text = "Browse...",
                Location = new Point(380, 178),
                Size = new Size(80, 25)
            };
            browseButton.Click += BrowseButton_Click;
            this.Controls.Add(browseButton);

            // Options
            desktopCheckBox = new CheckBox
            {
                Text = "Create desktop shortcut",
                Checked = true,
                Location = new Point(20, 220),
                Size = new Size(200, 20)
            };
            this.Controls.Add(desktopCheckBox);

            startMenuCheckBox = new CheckBox
            {
                Text = "Create Start Menu shortcuts",
                Checked = true,
                Location = new Point(20, 245),
                Size = new Size(200, 20)
            };
            this.Controls.Add(startMenuCheckBox);

            // Progress bar
            progressBar = new ProgressBar
            {
                Location = new Point(20, 280),
                Size = new Size(450, 20),
                Visible = false
            };
            this.Controls.Add(progressBar);

            // Status label
            statusLabel = new Label
            {
                Text = "Ready to install",
                Location = new Point(20, 305),
                Size = new Size(300, 20)
            };
            this.Controls.Add(statusLabel);

            // Buttons
            installButton = new Button
            {
                Text = "Install",
                Location = new Point(300, 350),
                Size = new Size(80, 30),
                BackColor = Color.LightGreen
            };
            installButton.Click += InstallButton_Click;
            this.Controls.Add(installButton);

            cancelButton = new Button
            {
                Text = "Cancel",
                Location = new Point(390, 350),
                Size = new Size(80, 30)
            };
            cancelButton.Click += (s, e) => this.Close();
            this.Controls.Add(cancelButton);
        }

        private void BrowseButton_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "Select installation folder";
                folderDialog.SelectedPath = Path.GetDirectoryName(pathTextBox.Text);
                
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    pathTextBox.Text = Path.Combine(folderDialog.SelectedPath, "OctopiProjectManager");
                }
            }
        }

        private async void InstallButton_Click(object sender, EventArgs e)
        {
            installButton.Enabled = false;
            cancelButton.Enabled = false;
            progressBar.Visible = true;
            progressBar.Style = ProgressBarStyle.Marquee;

            var progress = new Progress<string>(status =>
            {
                statusLabel.Text = status;
                Application.DoEvents();
            });

            try
            {
                bool success = await Task.Run(() => Program.InstallApplication(
                    pathTextBox.Text,
                    desktopCheckBox.Checked,
                    startMenuCheckBox.Checked,
                    progress));

                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 100;

                if (success)
                {
                    MessageBox.Show(
                        $"Octopi Project Manager has been installed successfully!\n\nInstallation Path: {pathTextBox.Text}",
                        "Installation Complete",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                    this.Close();
                }
                else
                {
                    MessageBox.Show(
                        "Installation failed. Please check the status message for details.",
                        "Installation Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                    
                    installButton.Enabled = true;
                    cancelButton.Enabled = true;
                    progressBar.Visible = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Installation failed: {ex.Message}",
                    "Installation Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                
                installButton.Enabled = true;
                cancelButton.Enabled = true;
                progressBar.Visible = false;
            }
        }
    }
}
