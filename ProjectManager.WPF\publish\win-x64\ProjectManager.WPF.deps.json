{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"ProjectManager.WPF/1.0.0": {"dependencies": {"CsvHelper": "33.1.0", "EPPlus": "8.0.7", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.36", "Microsoft.EntityFrameworkCore.Tools": "6.0.36", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "6.0.36", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "6.0.36"}, "runtime": {"ProjectManager.WPF.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.100.3624.51421"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.3624.51421"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "42.42.42.42424"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-console-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-fibers-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "clretwrc.dll": {"fileVersion": "6.0.3624.51421"}, "clrjit.dll": {"fileVersion": "6.0.3624.51421"}, "coreclr.dll": {"fileVersion": "6.0.3624.51421"}, "createdump.exe": {"fileVersion": "6.0.3624.51421"}, "dbgshim.dll": {"fileVersion": "6.0.3624.51421"}, "hostfxr.dll": {"fileVersion": "6.0.3624.51421"}, "hostpolicy.dll": {"fileVersion": "6.0.3624.51421"}, "mscordaccore.dll": {"fileVersion": "6.0.3624.51421"}, "mscordaccore_amd64_amd64_6.0.3624.51421.dll": {"fileVersion": "6.0.3624.51421"}, "mscordbi.dll": {"fileVersion": "6.0.3624.51421"}, "mscorrc.dll": {"fileVersion": "6.0.3624.51421"}, "msquic.dll": {"fileVersion": "*******"}, "ucrtbase.dll": {"fileVersion": "10.0.22000.194"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.36": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51513"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "6.0.3624.51603"}, "PresentationNative_cor3.dll": {"fileVersion": "6.0.24.46601"}, "vcruntime140_cor3.dll": {"fileVersion": "14.40.33810.0"}, "wpfgfx_cor3.dll": {"fileVersion": "6.0.3624.51603"}}}, "CsvHelper/33.1.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/CsvHelper.dll": {"assemblyVersion": "33.0.0.0", "fileVersion": "33.1.0.26"}}}, "EPPlus/8.0.7": {"dependencies": {"EPPlus.Interfaces": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.1/EPPlus.dll": {"assemblyVersion": "8.0.7.0", "fileVersion": "8.0.7.0"}}}, "EPPlus.Interfaces/8.0.0": {"runtime": {"lib/netstandard2.1/EPPlus.Interfaces.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Humanizer.Core/2.8.26": {}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.Sqlite.Core/6.0.36": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.36", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.36", "Microsoft.Extensions.Caching.Memory": "6.0.3", "Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.Logging": "6.0.1", "System.Collections.Immutable": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.2"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {}, "Microsoft.EntityFrameworkCore.Design/6.0.36": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.36"}}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.36", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "6.0.36", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.36": {"dependencies": {"Microsoft.Data.Sqlite.Core": "6.0.36", "Microsoft.EntityFrameworkCore.Relational": "6.0.36", "Microsoft.Extensions.DependencyModel": "6.0.2"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.36"}}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyModel/6.0.2": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "6.0.0.2", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.2"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}}}, "Microsoft.Extensions.Options/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "System.Buffers/4.5.1": {}, "System.Collections.Immutable/6.0.1": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Diagnostics.DiagnosticSource/6.0.2": {}, "System.Formats.Asn1/8.0.1": {"runtime": {"lib/net6.0/System.Formats.Asn1.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.724.31311"}}}, "System.Memory/4.5.4": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Pkcs/8.0.1": {"dependencies": {"System.Formats.Asn1": "8.0.1"}, "runtime": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Text.Encoding.CodePages/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}}}, "libraries": {"ProjectManager.WPF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "CsvHelper/33.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kqfTOZGrn7NarNeXgjh86JcpTHUoeQDMB8t9NVa/ZtlSYiV1rxfRnQ49WaJsob4AiGrbK0XDzpyKkBwai4F8eg==", "path": "csvhelper/33.1.0", "hashPath": "csvhelper.33.1.0.nupkg.sha512"}, "EPPlus/8.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nk+mFru50sM6GTE1EKoA0eSj07IHyL2yPwKIvxnNVvACv4qxCl48V1KP+R6wE2ncwOrZvqg3g3Fd+LnJvg6+dg==", "path": "epplus/8.0.7", "hashPath": "epplus.8.0.7.nupkg.sha512"}, "EPPlus.Interfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EFr/vUbDYK55sxjfUfLUiv7oiz1f6ZLRYMKILHyfnWS019cYX5zJaQ1U3OojRuED8tgEeXX9QeG7Kj/b0XE7hQ==", "path": "epplus.interfaces/8.0.0", "hashPath": "epplus.interfaces.8.0.0.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-Vh74dyfNQJBc58lcOuhSKH39AuQlgGatAY4bOCZx7F+8/GO3Ba2G9G1swCVhBQkeTG2Ftew8R6iga02ATZiE6w==", "path": "microsoft.data.sqlite.core/6.0.36", "hashPath": "microsoft.data.sqlite.core.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-0i1BBvJBrqdmIdTqCmL+/J74HucYqc5eck3J5trKe6AN2fvdE1lICto6HBwNhbtPniZO7bhW36FnIjTK0FamXg==", "path": "microsoft.entityframeworkcore/6.0.36", "hashPath": "microsoft.entityframeworkcore.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-nrR4lfe9izQK1eerKW/ECHuJV8xXtuvoj/APrwwOjX4+Ne2SMXBpetctPcYNVc3KyiKuUHJSLywWtsqoXE5ElA==", "path": "microsoft.entityframeworkcore.abstractions/6.0.36", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON>wloBaAJcLwgSW4hvGdAWjKnuIwgQ2PKTNkvG80PW/WFgedwKomY9wuO5BPewIHlX6huGyP//StQQRQOWr+Q==", "path": "microsoft.entityframeworkcore.analyzers/6.0.36", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-r9dwblLaEPSdHocaouGINBxfqPJieOCs4Z44gMXuWBCrlnfuteoXGp3lSZXtifSZCLdOcFMoUHpDgpSrk9CDmg==", "path": "microsoft.entityframeworkcore.design/6.0.36", "hashPath": "microsoft.entityframeworkcore.design.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-Vq9UQEFNU1t42AOSgz6bUe0fpMa1g4wO8Y7EfhYPX6VSriRSRB4ImTC2TEZjOUeyGcZyUI2Kyd6//RfUPUR+Pw==", "path": "microsoft.entityframeworkcore.relational/6.0.36", "hashPath": "microsoft.entityframeworkcore.relational.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-NWlTJgKQRrZcdN+vph/PSZ1e3a9LDvBgiO/WQLfMWwWEv0XdixMRh6hWaKI+QpqdVCtwzZPQovEBzhJBdgIasA==", "path": "microsoft.entityframeworkcore.sqlite/6.0.36", "hashPath": "microsoft.entityframeworkcore.sqlite.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-LQjAvbbFD673QABvnJXmGERqEg8NCEimSTPm17jaUVwG38V+rppXZqPnXoKhfV8XrOD7yneJ3+/1vA+cqtk7PQ==", "path": "microsoft.entityframeworkcore.sqlite.core/6.0.36", "hashPath": "microsoft.entityframeworkcore.sqlite.core.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-zBo/oepzDsoXCwfE4mwxzA3lBrYMtfpmkQ1rmtQnuvaORY6yCeVIx4Vpr/ENz3DShuHiNYr7c1ClBSZleW78Dw==", "path": "microsoft.entityframeworkcore.tools/6.0.36", "hashPath": "microsoft.entityframeworkcore.tools.6.0.36.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zEtKmOJ3sZ9YxRgvgLtoQsqO069Kqp0aC6Ai+DkyE5uahtu1ynvuoDVFQgyyhVcJWnxCzZHax/3AodvAx2mhjA==", "path": "microsoft.extensions.caching.abstractions/6.0.1", "hashPath": "microsoft.extensions.caching.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-GVNNcHoPDEUn4OQiBBGs5mE6nX7BA+LeQId9NeA+gB8xcbDUmFPAl8Er2ixNLbn4ffFr8t5jfMwdxgFG66k7BA==", "path": "microsoft.extensions.caching.memory/6.0.3", "hashPath": "microsoft.extensions.caching.memory.6.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-gWUfUZ2ZDvwiVCxsOMComAhG43xstNWWVjV2takUZYRuDSJjO9Q5/b3tfOSkl5mcVwZAL3RZviRj5ZilxHghlw==", "path": "microsoft.extensions.dependencyinjection/6.0.2", "hashPath": "microsoft.extensions.dependencyinjection.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-HS5YsudCGSVoCVdsYJ5FAO9vx0z04qSAXgVzpDJSQ1/w/X9q8hrQVGU2p+Yfui+2KcXLL+Zjc0SX3yJWtBmYiw==", "path": "microsoft.extensions.dependencymodel/6.0.2", "hashPath": "microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-k6tbYaHrqY9kq7p5FfpPbddY1OImPCpXQ/PGcED6N9s5ULRp8n1PdmMzsIwIzCnhIS5bs06G/lO9LfNVpUj8jg==", "path": "microsoft.extensions.logging/6.0.1", "hashPath": "microsoft.extensions.logging.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-K14wYgwOfKVELrUh5eBqlC8Wvo9vvhS3ZhIvcswV2uS/ubkTRPSQsN557EZiYUSSoZNxizG+alN4wjtdyLdcyw==", "path": "microsoft.extensions.logging.abstractions/6.0.4", "hashPath": "microsoft.extensions.logging.abstractions.6.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v5rh5jRcLBOKOaLVyYCm4TY/RoJlxWsW7N2TAPkmlHe55/0cB0Syp979x4He1+MIXsaTvJl1WOc7b1D1PSsO3A==", "path": "microsoft.extensions.options/6.0.1", "hashPath": "microsoft.extensions.options.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections.Immutable/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Cc+SfP+jyCmA1TnRnDA7cRZy5jbLyWodfuUJKDJ+PJZBwWkv8szz+ztSCHAonqnL01DRaHaS2ptc4bYIEdgvWw==", "path": "system.collections.immutable/6.0.1", "hashPath": "system.collections.immutable.6.0.1.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6tQaIexFycaotdGn23lf3XJ/eI1GOjQKIvQDRFN9N4pwoNsKnHuXccQ3lnQO6GX8KAb1ic+6ZofJmPdbUVwZag==", "path": "system.diagnostics.diagnosticsource/6.0.2", "hashPath": "system.diagnostics.diagnosticsource.6.0.2.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}