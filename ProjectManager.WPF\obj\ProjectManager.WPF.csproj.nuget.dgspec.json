{"format": 1, "restore": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\ProjectManager.WPF\\ProjectManager.WPF.csproj": {}}, "projects": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\ProjectManager.WPF\\ProjectManager.WPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\ProjectManager.WPF\\ProjectManager.WPF.csproj", "projectName": "ProjectManager.WPF", "projectPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\ProjectManager.WPF\\ProjectManager.WPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\ProjectManager.WPF\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[33.1.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.36, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}