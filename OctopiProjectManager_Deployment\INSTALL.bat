@echo off
echo.
echo ========================================
echo   Octopi Project Manager Installation
echo   Developed by <PERSON> (AntmanZA)
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo Note: Running without administrator privileges.
    echo Some features may require admin rights for first-time setup.
)

echo.
echo Installing Octopi Project Manager...
echo.

REM Create Program Files directory if it doesn't exist
if not exist "%ProgramFiles%\OctopiProjectManager" (
    mkdir "%ProgramFiles%\OctopiProjectManager"
    echo Created installation directory.
)

REM Copy files
copy "ProjectManager.WPF.exe" "%ProgramFiles%\OctopiProjectManager\" >nul
copy "ProjectManager.WPF.pdb" "%ProgramFiles%\OctopiProjectManager\" >nul

if %errorLevel% == 0 (
    echo Files copied successfully.
) else (
    echo Error copying files. Please run as administrator.
    pause
    exit /b 1
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Octopi Project Manager.lnk'); $Shortcut.TargetPath = '%ProgramFiles%\OctopiProjectManager\ProjectManager.WPF.exe'; $Shortcut.WorkingDirectory = '%ProgramFiles%\OctopiProjectManager'; $Shortcut.Description = 'Octopi Project Manager - Developed by Conrad Cloete'; $Shortcut.Save()"

REM Create Start Menu shortcut
echo Creating Start Menu shortcut...
if not exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager" (
    mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager"
)
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager\Octopi Project Manager.lnk'); $Shortcut.TargetPath = '%ProgramFiles%\OctopiProjectManager\ProjectManager.WPF.exe'; $Shortcut.WorkingDirectory = '%ProgramFiles%\OctopiProjectManager'; $Shortcut.Description = 'Octopi Project Manager - Developed by Conrad Cloete'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo.
echo You can now run Octopi Project Manager from:
echo - Desktop shortcut
echo - Start Menu
echo - Or directly: %ProgramFiles%\OctopiProjectManager\ProjectManager.WPF.exe
echo.
echo ========================================
echo.
pause
