﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Services;

namespace ProjectManager.WPF
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider? _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // Initialize database
            InitializeDatabase();

            // Create and show main window
            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        private void ConfigureServices(ServiceCollection services)
        {
            // Configure database
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "ProjectManagementApp",
                "ProjectManager.db");

            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);

            services.AddDbContext<ProjectManagerDbContext>(options =>
                options.UseSqlite($"Data Source={dbPath}"));

            // Register services
            services.AddScoped<DatabaseService>();
            services.AddScoped<Services.DepartmentService>();
            services.AddSingleton<SettingsService>();
            services.AddSingleton<ExcelService>();

            // Register windows
            services.AddTransient<MainWindow>();
        }

        private void InitializeDatabase()
        {
            using var scope = _serviceProvider!.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ProjectManagerDbContext>();
            var databaseService = scope.ServiceProvider.GetRequiredService<DatabaseService>();
            var settingsService = scope.ServiceProvider.GetRequiredService<SettingsService>();

            try
            {
                databaseService.InitializeDatabaseAsync().Wait();

                // Initialize theme based on saved settings
                ThemeManager.Instance.SetTheme(settingsService.Settings.DarkMode);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize database: {ex.Message}", "Database Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}
