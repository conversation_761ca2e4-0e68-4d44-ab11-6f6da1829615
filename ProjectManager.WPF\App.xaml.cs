﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Services;
using SQLitePCL;

namespace ProjectManager.WPF
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider? _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            try
            {
                // Initialize SQLite provider first
                InitializeSQLite();

                // Configure services
                var services = new ServiceCollection();
                ConfigureServices(services);
                _serviceProvider = services.BuildServiceProvider();

                // Initialize database
                InitializeDatabase();

                // Create and show main window
                var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Application startup failed: {ex.Message}", "Startup Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private void InitializeSQLite()
        {
            try
            {
                // Initialize SQLite provider for self-contained deployments
                SQLitePCL.Batteries.Init();

                // Test that SQLite is working
                using (var connection = new Microsoft.Data.Sqlite.SqliteConnection("Data Source=:memory:"))
                {
                    connection.Open();
                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                // If SQLite initialization fails, try alternative approach
                try
                {
                    raw.SetProvider(new SQLite3Provider_e_sqlite3());

                    // Test again
                    using (var connection = new Microsoft.Data.Sqlite.SqliteConnection("Data Source=:memory:"))
                    {
                        connection.Open();
                        connection.Close();
                    }
                }
                catch (Exception innerEx)
                {
                    throw new InvalidOperationException(
                        $"Failed to initialize SQLite provider. This may be due to missing native libraries.\n" +
                        $"Primary error: {ex.Message}\n" +
                        $"Secondary error: {innerEx.Message}\n\n" +
                        $"Please ensure that e_sqlite3.dll is present in the application directory.", ex);
                }
            }
        }

        private void ConfigureServices(ServiceCollection services)
        {
            try
            {
                // Configure database with better error handling
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                if (string.IsNullOrEmpty(documentsPath))
                {
                    // Fallback to application directory if Documents folder is not available
                    documentsPath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
                }

                var appDataPath = Path.Combine(documentsPath, "ProjectManagementApp");
                var dbPath = Path.Combine(appDataPath, "ProjectManager.db");

                // Ensure directory exists with proper error handling
                try
                {
                    Directory.CreateDirectory(appDataPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to create application data directory: {ex.Message}\nUsing application directory instead.",
                        "Directory Warning", MessageBoxButton.OK, MessageBoxImage.Warning);

                    // Fallback to application directory
                    var appDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
                    appDataPath = Path.Combine(appDir, "Data");
                    Directory.CreateDirectory(appDataPath);
                    dbPath = Path.Combine(appDataPath, "ProjectManager.db");
                }

                // Configure SQLite with proper connection string
                var connectionString = $"Data Source={dbPath};Cache=Shared;";

                services.AddDbContext<ProjectManagerDbContext>(options =>
                {
                    options.UseSqlite(connectionString);
                    options.EnableSensitiveDataLogging(false);
                    options.EnableServiceProviderCaching();
                });

                // Register services
                services.AddScoped<DatabaseService>();
                services.AddScoped<Services.DepartmentService>();
                services.AddSingleton<SettingsService>();
                services.AddSingleton<ExcelService>();

                // Register windows
                services.AddTransient<MainWindow>();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to configure services: {ex.Message}", "Configuration Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeDatabase()
        {
            try
            {
                using var scope = _serviceProvider!.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ProjectManagerDbContext>();
                var databaseService = scope.ServiceProvider.GetRequiredService<DatabaseService>();
                var settingsService = scope.ServiceProvider.GetRequiredService<SettingsService>();

                // Test database connection first
                try
                {
                    context.Database.CanConnect();
                }
                catch (Exception connEx)
                {
                    throw new InvalidOperationException($"Cannot connect to database: {connEx.Message}", connEx);
                }

                // Initialize database
                var initTask = databaseService.InitializeDatabaseAsync();
                initTask.Wait(TimeSpan.FromSeconds(30)); // Add timeout

                if (!initTask.IsCompletedSuccessfully)
                {
                    throw new TimeoutException("Database initialization timed out");
                }

                // Initialize theme based on saved settings
                try
                {
                    ThemeManager.Instance.SetTheme(settingsService.Settings.DarkMode);
                }
                catch (Exception themeEx)
                {
                    // Theme initialization failure shouldn't prevent app startup
                    System.Diagnostics.Debug.WriteLine($"Theme initialization failed: {themeEx.Message}");
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to initialize database: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nInner exception: {ex.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "Database Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}
