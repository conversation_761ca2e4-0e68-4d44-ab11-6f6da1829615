<UserControl x:Class="ProjectManager.WPF.Controls.GanttChart"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.Resources>
        <Style x:Key="GanttHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#2E3440"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
        
        <Style x:Key="GanttRowStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E5E9F0"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style x:Key="GanttBarStyle" TargetType="Rectangle">
            <Setter Property="Height" Value="20"/>
            <Setter Property="RadiusX" Value="3"/>
            <Setter Property="RadiusY" Value="3"/>
            <Setter Property="Stroke" Value="#4C566A"/>
            <Setter Property="StrokeThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E5E9F0" BorderThickness="0,0,0,1" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="View:" VerticalAlignment="Center" Margin="0,0,5,0" FontWeight="SemiBold"/>
                <ComboBox Name="cmbTimeScale" Width="120" Margin="0,0,10,0" SelectedIndex="1">
                    <ComboBoxItem Content="Days"/>
                    <ComboBoxItem Content="Weeks"/>
                    <ComboBoxItem Content="Months"/>
                </ComboBox>
                
                <Button Name="btnZoomIn" Content="🔍+" Width="30" Height="25" Margin="0,0,2,0" 
                        ToolTip="Zoom In" Click="BtnZoomIn_Click"/>
                <Button Name="btnZoomOut" Content="🔍-" Width="30" Height="25" Margin="0,0,10,0" 
                        ToolTip="Zoom Out" Click="BtnZoomOut_Click"/>
                
                <Button Name="btnFitToWindow" Content="📏 Fit" Height="25" Margin="0,0,10,0" 
                        ToolTip="Fit to Window" Click="BtnFitToWindow_Click"/>
                
                <TextBlock Text="Today:" VerticalAlignment="Center" Margin="0,0,5,0" FontWeight="SemiBold"/>
                <Button Name="btnGoToToday" Content="📅 Go to Today" Height="25" 
                        ToolTip="Go to Today" Click="BtnGoToToday_Click"/>
            </StackPanel>
        </Border>
        
        <!-- Main Gantt Area -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Project List Panel -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E5E9F0" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="40"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Project List Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E5E9F0" BorderThickness="0,0,0,1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Project Name" Style="{StaticResource GanttHeaderStyle}"/>
                            <TextBlock Grid.Column="1" Text="Team" Style="{StaticResource GanttHeaderStyle}"/>
                            <TextBlock Grid.Column="2" Text="Status" Style="{StaticResource GanttHeaderStyle}"/>
                        </Grid>
                    </Border>
                    
                    <!-- Project List -->
                    <ScrollViewer Grid.Row="1" Name="projectListScrollViewer" 
                                  VerticalScrollBarVisibility="Auto" 
                                  HorizontalScrollBarVisibility="Disabled">
                        <StackPanel Name="projectListPanel"/>
                    </ScrollViewer>
                </Grid>
            </Border>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" Background="#E5E9F0" 
                          HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
            
            <!-- Timeline Panel -->
            <Border Grid.Column="2" Background="White">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="40"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Timeline Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E5E9F0" BorderThickness="0,0,0,1">
                        <ScrollViewer Name="timelineHeaderScrollViewer" 
                                      HorizontalScrollBarVisibility="Hidden" 
                                      VerticalScrollBarVisibility="Hidden">
                            <Canvas Name="timelineHeaderCanvas" Height="40"/>
                        </ScrollViewer>
                    </Border>
                    
                    <!-- Timeline Content -->
                    <ScrollViewer Grid.Row="1" Name="timelineScrollViewer" 
                                  HorizontalScrollBarVisibility="Auto" 
                                  VerticalScrollBarVisibility="Auto"
                                  ScrollChanged="TimelineScrollViewer_ScrollChanged">
                        <Canvas Name="timelineCanvas" Background="White"/>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
