using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Views
{
    public partial class ProjectFormDialog : Window
    {
        public Project? Project { get; private set; }
        public bool IsEditMode { get; private set; }
        
        private readonly List<Team> _teams;
        private readonly Project? _existingProject;

        public ProjectFormDialog(List<Team> teams, Project? existingProject = null)
        {
            InitializeComponent();
            _teams = teams;
            _existingProject = existingProject;
            IsEditMode = existingProject != null;
            
            InitializeForm();
            LoadData();
        }

        private void InitializeForm()
        {
            // Set header text
            headerText.Text = IsEditMode ? "Edit Project" : "Add New Project";
            
            // Setup combo boxes
            cmbTeam.ItemsSource = _teams;
            cmbEquipmentType.ItemsSource = EquipmentType.All;
            cmbStatus.ItemsSource = ProjectStatus.All;
            
            // Set default values
            if (!IsEditMode)
            {
                cmbEquipmentType.SelectedIndex = 0;
                cmbStatus.SelectedItem = ProjectStatus.NotStarted;
                dpStartDate.SelectedDate = DateTime.Today;
                dpEndDate.SelectedDate = DateTime.Today.AddDays(7);
            }
        }

        private void LoadData()
        {
            if (IsEditMode && _existingProject != null)
            {
                txtProjectRef.Text = _existingProject.ProjectRef ?? string.Empty;
                txtProjectName.Text = _existingProject.Name;
                txtCustomerName.Text = _existingProject.CustomerName ?? string.Empty;
                txtDescription.Text = _existingProject.Description ?? string.Empty;
                cmbTeam.SelectedValue = _existingProject.TeamId;
                cmbEquipmentType.SelectedItem = _existingProject.EquipmentType;
                dpStartDate.SelectedDate = _existingProject.StartDate;
                dpEndDate.SelectedDate = _existingProject.EndDate;
                cmbStatus.SelectedItem = _existingProject.Status;
                txtEstimatedHours.Text = _existingProject.EstimatedHours?.ToString() ?? string.Empty;
                txtNotes.Text = _existingProject.Notes ?? string.Empty;
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    Project = CreateProjectFromForm();
                    DialogResult = true;
                    Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving project: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateForm()
        {
            bool isValid = true;
            
            // Clear previous error messages
            ClearErrorMessages();
            
            // Validate project name
            if (string.IsNullOrWhiteSpace(txtProjectName.Text))
            {
                errorProjectName.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            // Validate team selection
            if (cmbTeam.SelectedValue == null)
            {
                errorTeam.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            // Validate equipment type
            if (cmbEquipmentType.SelectedItem == null)
            {
                errorEquipmentType.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            // Validate start date
            if (dpStartDate.SelectedDate == null)
            {
                errorStartDate.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            // Validate end date
            if (dpEndDate.SelectedDate == null)
            {
                errorEndDate.Visibility = Visibility.Visible;
                isValid = false;
            }
            else if (dpStartDate.SelectedDate != null && dpEndDate.SelectedDate < dpStartDate.SelectedDate)
            {
                errorEndDate.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            // Validate estimated hours (if provided)
            if (!string.IsNullOrWhiteSpace(txtEstimatedHours.Text))
            {
                if (!decimal.TryParse(txtEstimatedHours.Text, out decimal hours) || hours < 0)
                {
                    errorEstimatedHours.Visibility = Visibility.Visible;
                    isValid = false;
                }
            }
            
            return isValid;
        }

        private void ClearErrorMessages()
        {
            errorProjectName.Visibility = Visibility.Collapsed;
            errorTeam.Visibility = Visibility.Collapsed;
            errorEquipmentType.Visibility = Visibility.Collapsed;
            errorStartDate.Visibility = Visibility.Collapsed;
            errorEndDate.Visibility = Visibility.Collapsed;
            errorEstimatedHours.Visibility = Visibility.Collapsed;
        }

        private Project CreateProjectFromForm()
        {
            var project = IsEditMode ? _existingProject! : new Project();

            project.ProjectRef = string.IsNullOrWhiteSpace(txtProjectRef.Text) ? null : txtProjectRef.Text.Trim();
            project.Name = txtProjectName.Text.Trim();
            project.CustomerName = string.IsNullOrWhiteSpace(txtCustomerName.Text) ? null : txtCustomerName.Text.Trim();
            project.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();
            project.TeamId = (int)cmbTeam.SelectedValue!;
            project.EquipmentType = cmbEquipmentType.SelectedItem!.ToString()!;
            project.StartDate = dpStartDate.SelectedDate!.Value;
            project.EndDate = dpEndDate.SelectedDate!.Value;
            project.Status = cmbStatus.SelectedItem?.ToString() ?? ProjectStatus.NotStarted;
            project.Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim();
            
            // Parse estimated hours
            if (!string.IsNullOrWhiteSpace(txtEstimatedHours.Text) && 
                decimal.TryParse(txtEstimatedHours.Text, out decimal hours))
            {
                project.EstimatedHours = hours;
            }
            else
            {
                project.EstimatedHours = null;
            }
            
            if (!IsEditMode)
            {
                project.CreatedDate = DateTime.Now;
            }
            else
            {
                project.ModifiedDate = DateTime.Now;
            }
            
            return project;
        }
    }
}
