using System;
using System.Windows;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Views
{
    public partial class TeamFormDialog : Window
    {
        public Team? Team { get; private set; }
        public bool IsEditMode { get; private set; }
        
        private readonly Team? _existingTeam;

        public TeamFormDialog(Team? existingTeam = null)
        {
            InitializeComponent();
            _existingTeam = existingTeam;
            IsEditMode = existingTeam != null;
            
            InitializeForm();
            LoadData();
        }

        private void InitializeForm()
        {
            // Set header text
            headerText.Text = IsEditMode ? "Edit Team" : "Add New Team";
        }

        private void LoadData()
        {
            if (IsEditMode && _existingTeam != null)
            {
                txtTeamName.Text = _existingTeam.Name;
                txtDescription.Text = _existingTeam.Description ?? string.Empty;
                chkIsActive.IsChecked = _existingTeam.IsActive;
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    Team = CreateTeamFromForm();
                    DialogResult = true;
                    Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving team: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateForm()
        {
            bool isValid = true;
            
            // Clear previous error messages
            ClearErrorMessages();
            
            // Validate team name
            if (string.IsNullOrWhiteSpace(txtTeamName.Text))
            {
                errorTeamName.Visibility = Visibility.Visible;
                isValid = false;
            }
            
            return isValid;
        }

        private void ClearErrorMessages()
        {
            errorTeamName.Visibility = Visibility.Collapsed;
        }

        private Team CreateTeamFromForm()
        {
            var team = IsEditMode ? _existingTeam! : new Team();
            
            team.Name = txtTeamName.Text.Trim();
            team.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();
            team.IsActive = chkIsActive.IsChecked ?? true;
            
            if (!IsEditMode)
            {
                team.CreatedDate = DateTime.Now;
            }
            
            return team;
        }
    }
}
