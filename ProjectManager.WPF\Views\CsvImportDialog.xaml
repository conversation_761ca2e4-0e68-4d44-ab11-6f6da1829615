<Window x:Class="ProjectManager.WPF.Views.CsvImportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="CSV Import" 
        Height="700" Width="900" 
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="StepHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#2E3440"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0">
            <TextBlock Text="Import Projects from CSV" Style="{StaticResource HeaderTextStyle}"/>
            <TextBlock Text="Follow the steps below to import your project data from a CSV file." 
                       FontSize="12" Foreground="#4C566A" Margin="0,0,0,20"/>
        </StackPanel>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Name="importTabControl" SelectionChanged="ImportTabControl_SelectionChanged">
            
            <!-- Step 1: File Selection -->
            <TabItem Header="1. Select File" Name="tabFileSelection">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Step 1: Select CSV File" Style="{StaticResource StepHeaderStyle}"/>
                    
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
                        <TextBox Name="txtFilePath" Width="400" Padding="8" IsReadOnly="True" 
                                 Background="#F8F9FA" BorderBrush="#E5E9F0"/>
                        <Button Name="btnBrowseFile" Content="Browse..." 
                                Style="{StaticResource ButtonStyle}"
                                Background="#5E81AC" Foreground="White" BorderBrush="Transparent"
                                Click="BtnBrowseFile_Click"/>
                        <Button Name="btnDownloadTemplate" Content="Download Template" 
                                Style="{StaticResource ButtonStyle}"
                                Background="#88C0D0" Foreground="White" BorderBrush="Transparent"
                                Click="BtnDownloadTemplate_Click"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="2" Text="CSV File Preview:" Style="{StaticResource StepHeaderStyle}"/>
                    
                    <DataGrid Grid.Row="3" Name="previewDataGrid" 
                              AutoGenerateColumns="True" 
                              IsReadOnly="True"
                              MaxHeight="300"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA"
                              RowBackground="White"
                              BorderBrush="#E5E9F0"
                              BorderThickness="1"/>
                </Grid>
            </TabItem>
            
            <!-- Step 2: Column Mapping -->
            <TabItem Header="2. Map Columns" Name="tabColumnMapping" IsEnabled="False">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Step 2: Map CSV Columns to Project Fields" Style="{StaticResource StepHeaderStyle}"/>
                    <TextBlock Grid.Row="1" Text="Map each CSV column to the corresponding project field. Required fields are marked with *" 
                               FontSize="11" Foreground="#4C566A" Margin="0,0,0,15"/>
                    
                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                        <StackPanel Name="mappingPanel">
                            <!-- Column mappings will be added dynamically -->
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- Step 3: Validation & Import -->
            <TabItem Header="3. Import" Name="tabImport" IsEnabled="False">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Step 3: Validation Results and Import" Style="{StaticResource StepHeaderStyle}"/>
                    
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                        <Button Name="btnValidate" Content="Validate Data" 
                                Style="{StaticResource ButtonStyle}"
                                Background="#EBCB8B" Foreground="Black" BorderBrush="Transparent"
                                Click="BtnValidate_Click"/>
                        <TextBlock Name="validationSummary" VerticalAlignment="Center" Margin="15,0,0,0" 
                                   FontWeight="SemiBold"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="2" Name="validationDataGrid" 
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA"
                              RowBackground="White"
                              BorderBrush="#E5E9F0"
                              BorderThickness="1">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Row" Binding="{Binding RowNumber}" Width="50"/>
                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="Project Name" Binding="{Binding ProjectName}" Width="150"/>
                            <DataGridTextColumn Header="Issues" Binding="{Binding IssuesText}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
                        <CheckBox Name="chkSkipErrors" Content="Skip rows with errors" 
                                  VerticalAlignment="Center" Margin="0,0,15,0"/>
                        <Button Name="btnStartImport" Content="Start Import" 
                                Style="{StaticResource ButtonStyle}"
                                Background="#A3BE8C" Foreground="White" BorderBrush="Transparent"
                                IsEnabled="False"
                                Click="BtnStartImport_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Navigation Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="btnPrevious" Content="← Previous" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#4C566A" Foreground="White" BorderBrush="Transparent"
                    IsEnabled="False"
                    Click="BtnPrevious_Click"/>
            <Button Name="btnNext" Content="Next →" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#5E81AC" Foreground="White" BorderBrush="Transparent"
                    IsEnabled="False"
                    Click="BtnNext_Click"/>
            <Button Name="btnCancel" Content="Cancel" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#BF616A" Foreground="White" BorderBrush="Transparent"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
