[Setup]
AppName=Octopi Project Manager
AppVersion=1.2.0
AppPublisher=<PERSON> (AntmanZA)
AppPublisherURL=https://octopi-smart-solutions.com
AppSupportURL=https://octopi-smart-solutions.com/support
AppUpdatesURL=https://octopi-smart-solutions.com/updates
DefaultDirName={autopf}\OctopiProjectManager
DefaultGroupName=Octopi Project Manager
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=Output
OutputBaseFilename=OctopiProjectManagerSetup_v1.2_DatabaseFix
SetupIconFile=octopi.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{group}\Octopi Project Manager"; Filename: "{app}\ProjectManager.WPF.exe"; IconFilename: "{app}\octopi.ico"
Name: "{group}\{cm:ProgramOnTheWeb,Octopi Project Manager}"; Filename: "https://octopi-smart-solutions.com"
Name: "{group}\{cm:UninstallProgram,Octopi Project Manager}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Octopi Project Manager"; Filename: "{app}\ProjectManager.WPF.exe"; IconFilename: "{app}\octopi.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Octopi Project Manager"; Filename: "{app}\ProjectManager.WPF.exe"; IconFilename: "{app}\octopi.ico"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\ProjectManager.WPF.exe"; Description: "{cm:LaunchProgram,Octopi Project Manager}"; Flags: nowait postinstall skipifsilent

[Registry]
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\OctopiProjectManager"; ValueType: string; ValueName: "DisplayName"; ValueData: "Octopi Project Manager v1.2.0"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\OctopiProjectManager"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "1.2.0"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\OctopiProjectManager"; ValueType: string; ValueName: "Publisher"; ValueData: "Conrad Cloete (AntmanZA)"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\OctopiProjectManager"; ValueType: string; ValueName: "InstallLocation"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\OctopiProjectManager"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\ProjectManager.WPF.exe"

[Code]
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := 'Welcome to Octopi Project Manager v1.2 Setup';
  WizardForm.WelcomeLabel2.Caption := 
    'This version includes important database fixes!' + #13#10 + #13#10 +
    '✅ FIXED: Database initialization errors' + #13#10 +
    '✅ Enhanced error handling and recovery' + #13#10 +
    '✅ Robust connection management' + #13#10 +
    '✅ Fallback path options' + #13#10 + #13#10 +
    'Features included:' + #13#10 +
    '• Professional project tracking' + #13#10 +
    '• Dynamic department management' + #13#10 +
    '• CSV/Excel import and export' + #13#10 +
    '• Interactive Gantt charts' + #13#10 +
    '• Dark and light themes' + #13#10 + #13#10 +
    'This will install Octopi Project Manager v1.2 on your computer.' + #13#10 + #13#10 +
    'It is recommended that you close all other applications before continuing.';
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if MsgBox('This installer will install Octopi Project Manager v1.2 with database fixes.' + #13#10 + #13#10 +
            'The previous database initialization errors have been resolved!' + #13#10 + #13#10 +
            'Do you want to continue?', mbConfirmation, MB_YESNO) = IDNO then
    Result := False;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Installation completed successfully!' + #13#10 + #13#10 +
           '✅ Database errors have been fixed!' + #13#10 +
           '✅ Application is ready to use!' + #13#10 + #13#10 +
           'The application will create its database automatically when first run.' + #13#10 +
           'Database location: Documents\ProjectManagementApp\' + #13#10 + #13#10 +
           'You can now launch Octopi Project Manager from:' + #13#10 +
           '• Desktop shortcut (if created)' + #13#10 +
           '• Start Menu' + #13#10 +
           '• Programs and Features', mbInformation, MB_OK);
  end;
end;
