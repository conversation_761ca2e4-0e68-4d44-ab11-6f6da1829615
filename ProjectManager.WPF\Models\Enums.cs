using System.Collections.Generic;

namespace ProjectManager.WPF.Models
{
    public static class ProjectStatus
    {
        public const string NotStarted = "Not Started";
        public const string InProgress = "In Progress";
        public const string Completed = "Completed";
        public const string OnHold = "On Hold";
        public const string Cancelled = "Cancelled";

        public static readonly string[] All = { NotStarted, InProgress, Completed, OnHold, Cancelled };
    }

    public static class Department
    {
        public const string ISP = "ISP";
        public const string Telco = "Telco";
        public const string CCTV = "CCTV";
        public const string Other = "Other";

        public static readonly string[] All = { ISP, Telco, CCTV, Other };
    }

    // Keep for backward compatibility
    public static class EquipmentType
    {
        public const string ISP = Department.ISP;
        public const string Telco = Department.Telco;
        public const string CCTV = Department.CCTV;
        public const string Other = Department.Other;

        public static readonly string[] All = Department.All;
    }

    public static class TaskPriority
    {
        public const int Low = 1;
        public const int Medium = 2;
        public const int High = 3;

        public static readonly Dictionary<int, string> Names = new()
        {
            { Low, "Low" },
            { Medium, "Medium" },
            { High, "High" }
        };
    }
}
