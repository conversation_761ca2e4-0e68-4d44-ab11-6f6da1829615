========================================
    OCTOPI PROJECT MANAGER v1.0
    Developed by <PERSON> (AntmanZA)
    Copyright © 2025 AntmanZa
========================================

ABOUT:
------
Octopi Project Manager is a comprehensive project management solution designed 
specifically for Octopi Smart Solutions. Track projects, manage timelines, 
and visualize progress with beautiful Gantt charts.

FEATURES:
---------
• Project and team management
• Interactive Gantt charts
• CSV/Excel import and export
• Dark and light themes
• Comprehensive reporting
• Department breakdown analysis

SYSTEM REQUIREMENTS:
-------------------
• Windows 10 or later (64-bit)
• No additional software required (self-contained)
• Minimum 4GB RAM recommended
• 200MB free disk space

INSTALLATION:
------------
1. Right-click on "INSTALL.bat" and select "Run as administrator"
2. Follow the on-screen instructions
3. The application will be installed to Program Files
4. Desktop and Start Menu shortcuts will be created

MANUAL INSTALLATION:
-------------------
If the installer doesn't work:
1. Copy "ProjectManager.WPF.exe" to any folder
2. Double-click to run the application
3. No additional setup required

UNINSTALLATION:
--------------
1. Right-click on "UNINSTALL.bat" and select "Run as administrator"
2. Follow the on-screen instructions
3. All program files and shortcuts will be removed

USAGE:
------
1. Launch the application from Desktop or Start Menu
2. Use the Projects tab to manage your projects
3. Import existing data using CSV/Excel import
4. View project timelines in the Gantt Chart tab
5. Generate reports in the Reports tab
6. Customize settings in the Settings tab

DATA STORAGE:
------------
• Application data is stored in a local SQLite database
• Database is created automatically on first run
• Data is preserved during updates and reinstallation

SUPPORT:
--------
For support or questions, contact:
Conrad Cloete (AntmanZA)

TECHNOLOGY:
----------
Built with:
• .NET 6 WPF
• Entity Framework Core
• SQLite Database
• EPPlus for Excel support
• CsvHelper for CSV processing

VERSION HISTORY:
---------------
v1.0 - Initial Release
• Complete project management functionality
• Gantt chart visualization
• Import/Export capabilities
• Multi-theme support

========================================
© 2025 AntmanZa - All Rights Reserved
========================================
