using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Data
{
    public class ProjectManagerDbContext : DbContext
    {
        public DbSet<Team> Teams { get; set; } = null!;
        public DbSet<DepartmentEntity> Departments { get; set; } = null!;
        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<ProjectTask> ProjectTasks { get; set; } = null!;

        public ProjectManagerDbContext(DbContextOptions<ProjectManagerDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Team entity
            modelBuilder.Entity<Team>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
            });

            // Configure Department entity
            modelBuilder.Entity<DepartmentEntity>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Color).HasDefaultValue("#5E81AC");
            });

            // Configure Project entity
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.StartDate).IsRequired();
                entity.Property(e => e.EndDate).IsRequired();
                entity.Property(e => e.EquipmentType).IsRequired();
                entity.Property(e => e.Status).IsRequired();

                // Configure relationship with Team
                entity.HasOne(p => p.Team)
                      .WithMany(t => t.Projects)
                      .HasForeignKey(p => p.TeamId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Configure relationship with Department (optional)
                entity.HasOne(p => p.Department)
                      .WithMany(d => d.Projects)
                      .HasForeignKey(p => p.DepartmentId)
                      .OnDelete(DeleteBehavior.SetNull);

                // Add check constraint for dates
                entity.HasCheckConstraint("CK_Project_EndDate", "[EndDate] >= [StartDate]");
            });

            // Configure ProjectTask entity
            modelBuilder.Entity<ProjectTask>(entity =>
            {
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.StartDate).IsRequired();
                entity.Property(e => e.EndDate).IsRequired();
                entity.Property(e => e.Status).IsRequired();

                // Configure relationship with Project
                entity.HasOne(pt => pt.Project)
                      .WithMany(p => p.Tasks)
                      .HasForeignKey(pt => pt.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with Team (optional)
                entity.HasOne(pt => pt.Team)
                      .WithMany(t => t.Tasks)
                      .HasForeignKey(pt => pt.TeamId)
                      .OnDelete(DeleteBehavior.SetNull);

                // Add check constraint for dates
                entity.HasCheckConstraint("CK_ProjectTask_EndDate", "[EndDate] >= [StartDate]");
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed default teams
            var teams = new List<Team>();
            for (int i = 1; i <= 20; i++)
            {
                teams.Add(new Team
                {
                    Id = i,
                    Name = $"Team {i}",
                    Description = $"Installation Team {i}",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                });
            }

            modelBuilder.Entity<Team>().HasData(teams);

            // Seed default departments
            var departments = new List<DepartmentEntity>
            {
                new DepartmentEntity { Id = 1, Name = "ISP", Description = "Internet Service Provider installations", Color = "#5E81AC", IsActive = true, CreatedDate = DateTime.Now },
                new DepartmentEntity { Id = 2, Name = "Telco", Description = "Telecommunications installations", Color = "#88C0D0", IsActive = true, CreatedDate = DateTime.Now },
                new DepartmentEntity { Id = 3, Name = "CCTV", Description = "Security camera installations", Color = "#A3BE8C", IsActive = true, CreatedDate = DateTime.Now },
                new DepartmentEntity { Id = 4, Name = "Other", Description = "Other types of installations", Color = "#EBCB8B", IsActive = true, CreatedDate = DateTime.Now }
            };

            modelBuilder.Entity<DepartmentEntity>().HasData(departments);
        }
    }
}
