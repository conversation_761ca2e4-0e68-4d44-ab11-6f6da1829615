<Window x:Class="ProjectManager.WPF.Views.TeamFormDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Team Details" 
        Height="350" Width="450" 
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Red"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="0,-10,0,10"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Name="headerText"
                   Text="Add New Team" 
                   FontSize="20" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>

        <!-- Form Content -->
        <StackPanel Grid.Row="1">
            <!-- Team Name -->
            <TextBlock Text="Team Name *" Style="{StaticResource LabelStyle}"/>
            <TextBox Name="txtTeamName" Style="{StaticResource InputStyle}" MaxLength="100"/>
            <TextBlock Name="errorTeamName" Text="Team name is required" Style="{StaticResource ErrorTextStyle}"/>

            <!-- Description -->
            <TextBlock Text="Description" Style="{StaticResource LabelStyle}"/>
            <TextBox Name="txtDescription" 
                     Style="{StaticResource InputStyle}" 
                     Height="80" 
                     TextWrapping="Wrap" 
                     AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto"
                     MaxLength="500"/>

            <!-- Active Status -->
            <CheckBox Name="chkIsActive" 
                      Content="Team is active" 
                      IsChecked="True" 
                      Margin="0,10,0,15"
                      FontWeight="SemiBold"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Name="btnSave" 
                    Content="Save" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#A3BE8C" 
                    Foreground="White" 
                    BorderBrush="Transparent"
                    Click="BtnSave_Click"/>
            <Button Name="btnCancel" 
                    Content="Cancel" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#4C566A" 
                    Foreground="White" 
                    BorderBrush="Transparent"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
