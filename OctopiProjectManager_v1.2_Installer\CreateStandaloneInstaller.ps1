# Octopi Project Manager v1.2 - Standalone Installer Creator
# This script creates a standalone installer executable

param(
    [string]$OutputPath = "OctopiProjectManagerSetup_v1.2_DatabaseFix.exe"
)

Write-Host "Creating Octopi Project Manager v1.2 Standalone Installer..." -ForegroundColor Green

# Check if we have the required files
$requiredFiles = @(
    "ProjectManager.WPF.exe",
    "octopi.ico",
    "README_v1.2_DATABASE_FIX.txt"
)

foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Error "Required file not found: $file"
        exit 1
    }
}

# Create installer script content
$installerScript = @'
using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using Microsoft.Win32;

namespace OctopiStandaloneInstaller
{
    class Program
    {
        private static string AppName = "Octopi Project Manager";
        private static string AppVersion = "1.2.0";
        private static string Publisher = "<PERSON>ete (AntmanZA)";
        private static string InstallPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "OctopiProjectManager");
        private static string AppExe = "ProjectManager.WPF.exe";

        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            if (args.Length > 0 && args[0].ToLower() == "/uninstall")
            {
                UninstallApplication();
                return;
            }

            try
            {
                ShowInstallDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Installation failed: {ex.Message}", 
                    "Installation Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void ShowInstallDialog()
        {
            var result = MessageBox.Show(
                $"Welcome to {AppName} v{AppVersion} Setup\n\n" +
                "✅ FIXED: Database initialization errors resolved!\n\n" +
                "This version includes:\n" +
                "• Enhanced database error handling\n" +
                "• Robust connection management\n" +
                "• Fallback path options\n" +
                "• Professional project tracking\n" +
                "• Dynamic department management\n" +
                "• CSV/Excel import and export\n" +
                "• Interactive Gantt charts\n" +
                "• Dark and light themes\n\n" +
                "Click OK to continue with installation.",
                $"Install {AppName} v{AppVersion}",
                MessageBoxButtons.OKCancel,
                MessageBoxIcon.Information);

            if (result == DialogResult.OK)
            {
                InstallApplication();
            }
        }

        private static void InstallApplication()
        {
            try
            {
                if (!Directory.Exists(InstallPath))
                {
                    Directory.CreateDirectory(InstallPath);
                }

                ExtractEmbeddedFiles();
                CreateShortcuts();
                RegisterApplication();

                MessageBox.Show(
                    $"{AppName} v{AppVersion} has been installed successfully!\n\n" +
                    "✅ Database errors have been fixed!\n\n" +
                    "You can now run the application from:\n" +
                    "• Desktop shortcut\n" +
                    "• Start Menu\n" +
                    "• Programs and Features\n\n" +
                    "The application will now start without database errors.",
                    "Installation Complete",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Installation failed: {ex.Message}", 
                    "Installation Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void ExtractEmbeddedFiles()
        {
            // For this simple version, we'll copy files from the same directory
            var currentDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var files = Directory.GetFiles(currentDir, "*.*", SearchOption.AllDirectories)
                .Where(f => !f.EndsWith(".exe") && !f.EndsWith(".pdb"))
                .ToArray();

            foreach (var file in files)
            {
                var relativePath = Path.GetRelativePath(currentDir, file);
                var destPath = Path.Combine(InstallPath, relativePath);
                var destDir = Path.GetDirectoryName(destPath);
                
                if (!Directory.Exists(destDir))
                {
                    Directory.CreateDirectory(destDir);
                }
                
                File.Copy(file, destPath, true);
            }
        }

        private static void CreateShortcuts()
        {
            try
            {
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var shortcutPath = Path.Combine(desktopPath, $"{AppName}.lnk");
                CreateShortcut(shortcutPath, Path.Combine(InstallPath, AppExe), InstallPath);

                var startMenuPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), AppName);
                if (!Directory.Exists(startMenuPath))
                {
                    Directory.CreateDirectory(startMenuPath);
                }

                var startMenuShortcut = Path.Combine(startMenuPath, $"{AppName}.lnk");
                CreateShortcut(startMenuShortcut, Path.Combine(InstallPath, AppExe), InstallPath);

                var uninstallShortcut = Path.Combine(startMenuPath, "Uninstall Octopi Project Manager.lnk");
                var installerPath = Assembly.GetExecutingAssembly().Location;
                CreateShortcut(uninstallShortcut, installerPath, Path.GetDirectoryName(installerPath), "/uninstall");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Shortcut creation failed: {ex.Message}");
            }
        }

        private static void CreateShortcut(string shortcutPath, string targetPath, string workingDirectory, string arguments = "")
        {
            try
            {
                var shell = Activator.CreateInstance(Type.GetTypeFromProgID("WScript.Shell"));
                var shortcut = shell.GetType().InvokeMember("CreateShortcut", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shell, new object[] { shortcutPath });

                shortcut.GetType().InvokeMember("TargetPath", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { targetPath });
                shortcut.GetType().InvokeMember("WorkingDirectory", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { workingDirectory });
                shortcut.GetType().InvokeMember("Description", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { $"{AppName} - Project Tracker v{AppVersion}" });
                
                if (!string.IsNullOrEmpty(arguments))
                {
                    shortcut.GetType().InvokeMember("Arguments", 
                        System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { arguments });
                }

                shortcut.GetType().InvokeMember("Save", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shortcut, null);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create shortcut {shortcutPath}: {ex.Message}");
            }
        }

        private static void RegisterApplication()
        {
            try
            {
                var uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                using (var key = Registry.LocalMachine.CreateSubKey(uninstallKey))
                {
                    key.SetValue("DisplayName", $"{AppName} v{AppVersion}");
                    key.SetValue("DisplayVersion", AppVersion);
                    key.SetValue("Publisher", Publisher);
                    key.SetValue("InstallLocation", InstallPath);
                    key.SetValue("DisplayIcon", Path.Combine(InstallPath, AppExe));
                    key.SetValue("UninstallString", $"\"{Assembly.GetExecutingAssembly().Location}\" /uninstall");
                    key.SetValue("EstimatedSize", 160000, RegistryValueKind.DWord);
                    key.SetValue("NoModify", 1, RegistryValueKind.DWord);
                    key.SetValue("NoRepair", 1, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to register application: {ex.Message}");
            }
        }

        private static void UninstallApplication()
        {
            try
            {
                var result = MessageBox.Show(
                    $"Are you sure you want to uninstall {AppName}?\n\n" +
                    "This will remove:\n" +
                    "• Application files\n" +
                    "• Desktop and Start Menu shortcuts\n" +
                    "• Registry entries\n\n" +
                    "Your project data will be preserved.",
                    "Uninstall Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                var desktopShortcut = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), $"{AppName}.lnk");
                if (File.Exists(desktopShortcut))
                    File.Delete(desktopShortcut);

                var startMenuPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), AppName);
                if (Directory.Exists(startMenuPath))
                    Directory.Delete(startMenuPath, true);

                var uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                Registry.LocalMachine.DeleteSubKey(uninstallKey, false);

                if (Directory.Exists(InstallPath))
                {
                    var filesToRemove = Directory.GetFiles(InstallPath, "*.*", SearchOption.AllDirectories)
                        .Where(f => !f.EndsWith(".db") && !f.EndsWith(".db-journal") && !f.EndsWith(".db-wal"))
                        .ToArray();

                    foreach (var file in filesToRemove)
                    {
                        try { File.Delete(file); } catch { }
                    }

                    try
                    {
                        if (!Directory.EnumerateFileSystemEntries(InstallPath).Any())
                            Directory.Delete(InstallPath);
                    }
                    catch { }
                }

                MessageBox.Show(
                    $"{AppName} has been uninstalled successfully!\n\n" +
                    "Your project data has been preserved.",
                    "Uninstall Complete",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uninstall failed: {ex.Message}", 
                    "Uninstall Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
'@

# Save the installer script
$installerScript | Out-File -FilePath "InstallerProgram.cs" -Encoding UTF8

Write-Host "Installer script created successfully!" -ForegroundColor Green
Write-Host "Files ready for packaging in: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""
Write-Host "To create the installer:" -ForegroundColor Cyan
Write-Host "1. Use a tool like IExpress (built into Windows)" -ForegroundColor White
Write-Host "2. Or use NSIS, Inno Setup, or similar installer creator" -ForegroundColor White
Write-Host "3. Package all files in this directory into a self-extracting executable" -ForegroundColor White
Write-Host ""
Write-Host "The installer will:" -ForegroundColor Green
Write-Host "✅ Install to Program Files" -ForegroundColor White
Write-Host "✅ Create desktop and start menu shortcuts" -ForegroundColor White
Write-Host "✅ Register in Programs and Features" -ForegroundColor White
Write-Host "✅ Include uninstaller functionality" -ForegroundColor White
Write-Host "✅ Preserve user data during uninstall" -ForegroundColor White
