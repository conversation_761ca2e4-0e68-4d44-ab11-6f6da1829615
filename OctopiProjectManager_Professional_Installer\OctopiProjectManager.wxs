<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  
  <!-- Product Definition -->
  <Product Id="*" 
           Name="Octopi Project Manager" 
           Language="1033" 
           Version="*******" 
           Manufacturer="<PERSON> (AntmanZA)" 
           UpgradeCode="12345678-1234-1234-1234-123456789012">
    
    <!-- Package Information -->
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine"
             Description="Octopi Project Manager - Comprehensive project management solution"
             Comments="Developed by <PERSON> (AntmanZA)"
             Manufacturer="<PERSON> (AntmanZA)" />

    <!-- Media and Cabinet -->
    <Media Id="1" Cabinet="OctopiProjectManager.cab" EmbedCab="yes" />

    <!-- Major Upgrade -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />

    <!-- Directory Structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="OctopiProjectManager" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Octopi Project Manager" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>

    <!-- Components -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      
      <!-- Main Application -->
      <Component Id="MainExecutable" Guid="*">
        <File Id="ProjectManagerEXE" 
              Source="../OctopiProjectManager_Deployment/ProjectManager.WPF.exe" 
              KeyPath="yes" 
              Checksum="yes">
          
          <!-- File Association -->
          <Shortcut Id="ApplicationStartMenuShortcut"
                    Directory="ApplicationProgramsFolder"
                    Name="Octopi Project Manager"
                    Description="Octopi Project Manager - Project Tracker"
                    WorkingDirectory="INSTALLFOLDER"
                    Icon="OctopiIcon.exe"
                    IconIndex="0"
                    Advertise="yes" />
          
          <!-- Desktop Shortcut -->
          <Shortcut Id="ApplicationDesktopShortcut"
                    Directory="DesktopFolder"
                    Name="Octopi Project Manager"
                    Description="Octopi Project Manager - Project Tracker"
                    WorkingDirectory="INSTALLFOLDER"
                    Icon="OctopiIcon.exe"
                    IconIndex="0"
                    Advertise="yes" />
        </File>
      </Component>

      <!-- Debug Symbols (Optional) -->
      <Component Id="DebugSymbols" Guid="*">
        <File Id="ProjectManagerPDB" 
              Source="../OctopiProjectManager_Deployment/ProjectManager.WPF.pdb" 
              KeyPath="yes" />
      </Component>

    </ComponentGroup>

    <!-- Start Menu Folder -->
    <DirectoryRef Id="ApplicationProgramsFolder">
      <Component Id="ApplicationShortcuts" Guid="*">
        <Shortcut Id="UninstallProduct"
                  Name="Uninstall Octopi Project Manager"
                  Description="Uninstalls Octopi Project Manager"
                  Target="[SystemFolder]msiexec.exe"
                  Arguments="/x [ProductCode]" />
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
        <RegistryValue Root="HKCU" 
                       Key="Software\OctopiProjectManager" 
                       Name="installed" 
                       Type="integer" 
                       Value="1" 
                       KeyPath="yes" />
      </Component>
    </DirectoryRef>

    <!-- Features -->
    <Feature Id="ProductFeature" Title="Octopi Project Manager" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcuts" />
    </Feature>

    <!-- Icon -->
    <Icon Id="OctopiIcon.exe" SourceFile="../OctopiProjectManager_Deployment/ProjectManager.WPF.exe" />
    <Property Id="ARPPRODUCTICON" Value="OctopiIcon.exe" />

    <!-- Add/Remove Programs Information -->
    <Property Id="ARPHELPLINK" Value="https://github.com/conradcloete" />
    <Property Id="ARPURLINFOABOUT" Value="https://github.com/conradcloete" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

    <!-- UI -->
    <UIRef Id="WixUI_InstallDir" />
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />

    <!-- License -->
    <WixVariable Id="WixUILicenseRtf" Value="License.rtf" />

  </Product>
</Wix>
