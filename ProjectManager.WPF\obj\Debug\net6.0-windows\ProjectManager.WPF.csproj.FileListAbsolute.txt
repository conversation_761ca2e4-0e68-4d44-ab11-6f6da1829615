C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\ProjectManager.WPF.exe
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\ProjectManager.WPF.deps.json
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\ProjectManager.WPF.runtimeconfig.json
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\ProjectManager.WPF.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\ProjectManager.WPF.pdb
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\CsvHelper.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\EPPlus.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Humanizer.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Data.Sqlite.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Design.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Memory.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyModel.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\SQLitePCLRaw.batteries_v2.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\SQLitePCLRaw.core.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Collections.Immutable.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Formats.Asn1.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Security.Cryptography.Xml.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Text.Encoding.CodePages.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Text.Encodings.Web.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\System.Text.Json.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\alpine-arm\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\alpine-arm64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\alpine-x64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win-arm\native\e_sqlite3.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win-x64\native\e_sqlite3.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win-x86\native\e_sqlite3.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Text.Encoding.CodePages.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\bin\Debug\net6.0-windows\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.csproj.AssemblyReference.cache
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Controls\GanttChart.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\AboutDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\CsvImportDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\ProjectFormDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\TeamFormDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Controls\GanttChart.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\MainWindow.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\AboutDialog.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\CsvImportDialog.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\ProjectFormDialog.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\TeamFormDialog.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\App.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\GeneratedInternalTypeHelper.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF_MarkupCompile.cache
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF_MarkupCompile.lref
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\MainWindow.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.g.resources
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.GeneratedMSBuildEditorConfig.editorconfig
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.AssemblyInfoInputs.cache
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.AssemblyInfo.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.csproj.CoreCompileInputs.cache
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.csproj.CopyComplete
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\refint\ProjectManager.WPF.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.pdb
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ProjectManager.WPF.genruntimeconfig.cache
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\ref\ProjectManager.WPF.dll
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\DepartmentFormDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\DepartmentManagementDialog.baml
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\DepartmentFormDialog.g.cs
C:\MyDocuments\ConradC\OWN Builds\Projectmanager\ProjectManager.WPF\obj\Debug\net6.0-windows\Views\DepartmentManagementDialog.g.cs
