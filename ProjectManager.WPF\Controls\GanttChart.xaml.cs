using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Controls
{
    public partial class GanttChart : UserControl
    {
        private List<Project> _projects = new();
        private DateTime _startDate;
        private DateTime _endDate;
        private double _dayWidth = 30;
        private double _rowHeight = 35;
        private double _zoomFactor = 1.0;
        
        public event EventHandler<Project>? ProjectClicked;
        
        public GanttChart()
        {
            InitializeComponent();
            InitializeChart();
        }
        
        private void InitializeChart()
        {
            cmbTimeScale.SelectionChanged += CmbTimeScale_SelectionChanged;
            
            // Sync scrolling between project list and timeline
            projectListScrollViewer.ScrollChanged += (s, e) =>
            {
                if (e.VerticalChange != 0)
                {
                    timelineScrollViewer.ScrollToVerticalOffset(e.VerticalOffset);
                }
            };
        }
        
        public void LoadProjects(List<Project> projects)
        {
            _projects = projects.Where(p => p != null).ToList();
            
            if (!_projects.Any())
            {
                ClearChart();
                return;
            }
            
            CalculateDateRange();
            RenderChart();
        }
        
        private void CalculateDateRange()
        {
            if (!_projects.Any()) return;
            
            _startDate = _projects.Min(p => p.StartDate).AddDays(-7); // Add some padding
            _endDate = _projects.Max(p => p.EndDate).AddDays(7);
            
            // Ensure minimum 30-day range
            if ((_endDate - _startDate).TotalDays < 30)
            {
                var center = _startDate.AddDays((_endDate - _startDate).TotalDays / 2);
                _startDate = center.AddDays(-15);
                _endDate = center.AddDays(15);
            }
        }
        
        private void RenderChart()
        {
            ClearChart();
            RenderTimelineHeader();
            RenderProjectRows();
            RenderGanttBars();
            RenderTodayLine();
        }
        
        private void ClearChart()
        {
            projectListPanel.Children.Clear();
            timelineCanvas.Children.Clear();
            timelineHeaderCanvas.Children.Clear();
        }
        
        private void RenderTimelineHeader()
        {
            var totalDays = (_endDate - _startDate).TotalDays;
            var canvasWidth = totalDays * _dayWidth * _zoomFactor;
            
            timelineHeaderCanvas.Width = canvasWidth;
            timelineCanvas.Width = canvasWidth;
            
            var currentDate = _startDate;
            var x = 0.0;
            
            while (currentDate <= _endDate)
            {
                // Draw month headers
                if (currentDate.Day == 1 || currentDate == _startDate)
                {
                    var monthText = new TextBlock
                    {
                        Text = currentDate.ToString("MMM yyyy"),
                        FontWeight = FontWeights.Bold,
                        FontSize = 11,
                        Foreground = new SolidColorBrush(Color.FromRgb(0x2E, 0x34, 0x40))
                    };
                    
                    Canvas.SetLeft(monthText, x + 5);
                    Canvas.SetTop(monthText, 2);
                    timelineHeaderCanvas.Children.Add(monthText);
                    
                    // Month separator line
                    var monthLine = new Line
                    {
                        X1 = x, Y1 = 0, X2 = x, Y2 = 40,
                        Stroke = new SolidColorBrush(Color.FromRgb(0x4C, 0x56, 0x6A)),
                        StrokeThickness = 1
                    };
                    timelineHeaderCanvas.Children.Add(monthLine);
                }
                
                // Draw day markers (for week/day view)
                if (cmbTimeScale.SelectedIndex <= 1) // Days or Weeks
                {
                    var dayText = new TextBlock
                    {
                        Text = currentDate.Day.ToString(),
                        FontSize = 9,
                        Foreground = new SolidColorBrush(Color.FromRgb(0x4C, 0x56, 0x6A))
                    };
                    
                    Canvas.SetLeft(dayText, x + 2);
                    Canvas.SetTop(dayText, 22);
                    timelineHeaderCanvas.Children.Add(dayText);
                    
                    // Day separator line
                    if (currentDate.DayOfWeek == DayOfWeek.Monday)
                    {
                        var dayLine = new Line
                        {
                            X1 = x, Y1 = 20, X2 = x, Y2 = 40,
                            Stroke = new SolidColorBrush(Color.FromRgb(0xE5, 0xE9, 0xF0)),
                            StrokeThickness = 1
                        };
                        timelineHeaderCanvas.Children.Add(dayLine);
                    }
                }
                
                currentDate = currentDate.AddDays(1);
                x += _dayWidth * _zoomFactor;
            }
        }
        
        private void RenderProjectRows()
        {
            var y = 0.0;
            
            foreach (var project in _projects)
            {
                // Create project row in left panel
                var projectRow = CreateProjectRow(project);
                projectListPanel.Children.Add(projectRow);
                
                // Draw row background in timeline
                var rowBackground = new Rectangle
                {
                    Width = timelineCanvas.Width,
                    Height = _rowHeight,
                    Fill = y % (2 * _rowHeight) == 0 ? 
                        new SolidColorBrush(Colors.White) : 
                        new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA)),
                    Stroke = new SolidColorBrush(Color.FromRgb(0xE5, 0xE9, 0xF0)),
                    StrokeThickness = 0.5
                };
                
                Canvas.SetLeft(rowBackground, 0);
                Canvas.SetTop(rowBackground, y);
                timelineCanvas.Children.Add(rowBackground);
                
                y += _rowHeight;
            }
            
            timelineCanvas.Height = y;
        }
        
        private Border CreateProjectRow(Project project)
        {
            var border = new Border
            {
                Height = _rowHeight,
                BorderBrush = new SolidColorBrush(Color.FromRgb(0xE5, 0xE9, 0xF0)),
                BorderThickness = new Thickness(0, 0, 0, 1),
                Background = new SolidColorBrush(Colors.White)
            };
            
            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
            
            var nameText = new TextBlock
            {
                Text = project.Name,
                FontSize = 11,
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 0, 0),
                TextTrimming = TextTrimming.CharacterEllipsis
            };
            Grid.SetColumn(nameText, 0);
            
            var teamText = new TextBlock
            {
                Text = project.Team?.Name ?? "N/A",
                FontSize = 10,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 0, 0),
                TextTrimming = TextTrimming.CharacterEllipsis
            };
            Grid.SetColumn(teamText, 1);
            
            var statusText = new TextBlock
            {
                Text = project.Status,
                FontSize = 10,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 0, 0),
                Foreground = GetStatusColor(project.Status)
            };
            Grid.SetColumn(statusText, 2);
            
            grid.Children.Add(nameText);
            grid.Children.Add(teamText);
            grid.Children.Add(statusText);
            
            border.Child = grid;
            return border;
        }
        
        private Brush GetStatusColor(string status)
        {
            return status switch
            {
                "Completed" => new SolidColorBrush(Color.FromRgb(0xA3, 0xBE, 0x8C)),
                "In Progress" => new SolidColorBrush(Color.FromRgb(0xEB, 0xCB, 0x8B)),
                "On Hold" => new SolidColorBrush(Color.FromRgb(0xBF, 0x61, 0x6A)),
                _ => new SolidColorBrush(Color.FromRgb(0x4C, 0x56, 0x6A))
            };
        }

        private void RenderGanttBars()
        {
            var y = 7.5; // Center bars in rows

            foreach (var project in _projects)
            {
                var startX = (project.StartDate - _startDate).TotalDays * _dayWidth * _zoomFactor;
                var duration = (project.EndDate - project.StartDate).TotalDays;
                var width = Math.Max(duration * _dayWidth * _zoomFactor, 10); // Minimum width

                var ganttBar = new Rectangle
                {
                    Width = width,
                    Height = 20,
                    Fill = GetProjectColor(project),
                    Stroke = new SolidColorBrush(Color.FromRgb(0x4C, 0x56, 0x6A)),
                    StrokeThickness = 1,
                    RadiusX = 3,
                    RadiusY = 3,
                    Cursor = Cursors.Hand,
                    Tag = project
                };

                ganttBar.MouseLeftButtonDown += GanttBar_MouseLeftButtonDown;
                ganttBar.ToolTip = CreateGanttBarTooltip(project);

                Canvas.SetLeft(ganttBar, startX);
                Canvas.SetTop(ganttBar, y);
                timelineCanvas.Children.Add(ganttBar);

                // Add project name on bar if there's space
                if (width > 60)
                {
                    var barText = new TextBlock
                    {
                        Text = project.Name,
                        FontSize = 9,
                        FontWeight = FontWeights.SemiBold,
                        Foreground = new SolidColorBrush(Colors.White),
                        VerticalAlignment = VerticalAlignment.Center,
                        IsHitTestVisible = false
                    };

                    Canvas.SetLeft(barText, startX + 5);
                    Canvas.SetTop(barText, y + 5);
                    timelineCanvas.Children.Add(barText);
                }

                y += _rowHeight;
            }
        }

        private Brush GetProjectColor(Project project)
        {
            return project.EquipmentType switch
            {
                "ISP" => new SolidColorBrush(Color.FromRgb(0x5E, 0x81, 0xAC)),
                "Telco" => new SolidColorBrush(Color.FromRgb(0x88, 0xC0, 0xD0)),
                "CCTV" => new SolidColorBrush(Color.FromRgb(0xA3, 0xBE, 0x8C)),
                "Other" => new SolidColorBrush(Color.FromRgb(0xEB, 0xCB, 0x8B)),
                _ => new SolidColorBrush(Color.FromRgb(0x81, 0xA1, 0xC1))
            };
        }

        private ToolTip CreateGanttBarTooltip(Project project)
        {
            var tooltip = new ToolTip();
            var panel = new StackPanel();

            panel.Children.Add(new TextBlock
            {
                Text = project.Name,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 5)
            });

            panel.Children.Add(new TextBlock
            {
                Text = $"Team: {project.Team?.Name ?? "N/A"}"
            });

            panel.Children.Add(new TextBlock
            {
                Text = $"Equipment: {project.EquipmentType}"
            });

            panel.Children.Add(new TextBlock
            {
                Text = $"Status: {project.Status}"
            });

            panel.Children.Add(new TextBlock
            {
                Text = $"Duration: {project.StartDate:MMM dd} - {project.EndDate:MMM dd}"
            });

            if (project.EstimatedHours.HasValue)
            {
                panel.Children.Add(new TextBlock
                {
                    Text = $"Estimated Hours: {project.EstimatedHours:F1}"
                });
            }

            tooltip.Content = panel;
            return tooltip;
        }

        private void RenderTodayLine()
        {
            var today = DateTime.Today;
            if (today >= _startDate && today <= _endDate)
            {
                var x = (today - _startDate).TotalDays * _dayWidth * _zoomFactor;

                var todayLine = new Line
                {
                    X1 = x, Y1 = 0, X2 = x, Y2 = timelineCanvas.Height,
                    Stroke = new SolidColorBrush(Color.FromRgb(0xBF, 0x61, 0x6A)),
                    StrokeThickness = 2,
                    StrokeDashArray = new DoubleCollection { 5, 5 }
                };

                timelineCanvas.Children.Add(todayLine);

                // Today label
                var todayLabel = new TextBlock
                {
                    Text = "TODAY",
                    FontSize = 10,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(0xBF, 0x61, 0x6A)),
                    Background = new SolidColorBrush(Colors.White),
                    Padding = new Thickness(2)
                };

                Canvas.SetLeft(todayLabel, x + 5);
                Canvas.SetTop(todayLabel, 5);
                timelineCanvas.Children.Add(todayLabel);
            }
        }

        // Event Handlers
        private void GanttBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Rectangle bar && bar.Tag is Project project)
            {
                ProjectClicked?.Invoke(this, project);
            }
        }

        private void TimelineScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // Sync horizontal scrolling with header
            if (e.HorizontalChange != 0)
            {
                timelineHeaderScrollViewer.ScrollToHorizontalOffset(e.HorizontalOffset);
            }

            // Sync vertical scrolling with project list
            if (e.VerticalChange != 0)
            {
                projectListScrollViewer.ScrollToVerticalOffset(e.VerticalOffset);
            }
        }

        private void CmbTimeScale_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateTimeScale();
        }

        private void UpdateTimeScale()
        {
            _dayWidth = cmbTimeScale.SelectedIndex switch
            {
                0 => 40, // Days
                1 => 30, // Weeks
                2 => 20, // Months
                _ => 30
            };

            if (_projects.Any())
            {
                RenderChart();
            }
        }

        private void BtnZoomIn_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor = Math.Min(_zoomFactor * 1.2, 3.0);
            if (_projects.Any())
            {
                RenderChart();
            }
        }

        private void BtnZoomOut_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor = Math.Max(_zoomFactor / 1.2, 0.3);
            if (_projects.Any())
            {
                RenderChart();
            }
        }

        private void BtnFitToWindow_Click(object sender, RoutedEventArgs e)
        {
            if (!_projects.Any()) return;

            var availableWidth = timelineScrollViewer.ActualWidth - 20; // Account for scrollbar
            var totalDays = (_endDate - _startDate).TotalDays;
            var optimalDayWidth = availableWidth / totalDays;

            _dayWidth = Math.Max(Math.Min(optimalDayWidth, 50), 10);
            _zoomFactor = 1.0;

            RenderChart();
        }

        private void BtnGoToToday_Click(object sender, RoutedEventArgs e)
        {
            var today = DateTime.Today;
            if (today >= _startDate && today <= _endDate)
            {
                var x = (today - _startDate).TotalDays * _dayWidth * _zoomFactor;
                var centerX = x - (timelineScrollViewer.ActualWidth / 2);

                timelineScrollViewer.ScrollToHorizontalOffset(Math.Max(0, centerX));
            }
        }

        // Public methods for filtering
        public void FilterByTeam(int? teamId)
        {
            var filteredProjects = teamId.HasValue && teamId.Value > 0
                ? _projects.Where(p => p.TeamId == teamId.Value).ToList()
                : _projects;

            LoadProjects(filteredProjects);
        }

        public void FilterByEquipmentType(string? equipmentType)
        {
            var filteredProjects = !string.IsNullOrEmpty(equipmentType) && equipmentType != "All Types"
                ? _projects.Where(p => p.EquipmentType == equipmentType).ToList()
                : _projects;

            LoadProjects(filteredProjects);
        }

        public void ClearFilters()
        {
            LoadProjects(_projects);
        }
    }
}
