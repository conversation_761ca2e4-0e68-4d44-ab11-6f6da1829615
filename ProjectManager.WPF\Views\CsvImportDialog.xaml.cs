using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Win32;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Views
{
    public partial class CsvImportDialog : Window
    {
        private readonly List<Team> _teams;
        private string _csvFilePath = string.Empty;
        private List<Dictionary<string, object>> _csvData = new();
        private Dictionary<string, string> _columnMappings = new();
        private List<ValidationResult> _validationResults = new();
        
        public List<Project> ImportedProjects { get; private set; } = new();
        public bool ImportSuccessful { get; private set; }

        public CsvImportDialog(List<Team> teams)
        {
            InitializeComponent();
            _teams = teams;
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            // Set initial tab
            importTabControl.SelectedIndex = 0;
            UpdateNavigationButtons();
        }

        private void ImportTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateNavigationButtons();
        }

        private void UpdateNavigationButtons()
        {
            int selectedIndex = importTabControl.SelectedIndex;

            btnPrevious.IsEnabled = selectedIndex > 0;
            btnNext.IsEnabled = false;

            switch (selectedIndex)
            {
                case 0: // File Selection
                    btnNext.Content = "Next →";
                    btnNext.IsEnabled = !string.IsNullOrEmpty(_csvFilePath) && _csvData.Any();
                    break;
                case 1: // Column Mapping
                    btnNext.Content = "Next →";
                    btnNext.IsEnabled = ValidateColumnMappings();
                    break;
                case 2: // Import
                    btnNext.Content = "Import";
                    btnNext.IsEnabled = true; // Always allow import on the final tab
                    break;
            }
        }

        private void BtnBrowseFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                Title = "Select CSV file to import"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadCsvFile(openFileDialog.FileName);
            }
        }

        private async void LoadCsvFile(string filePath)
        {
            try
            {
                _csvFilePath = filePath;
                txtFilePath.Text = filePath;
                
                _csvData = await Task.Run(() => ReadCsvFile(filePath));
                
                if (_csvData.Any())
                {
                    // Show preview (first 10 rows)
                    var previewData = _csvData.Take(10).ToList();
                    var dataTable = ConvertToDataTable(previewData);
                    previewDataGrid.ItemsSource = dataTable.DefaultView;
                    
                    // Enable column mapping tab
                    tabColumnMapping.IsEnabled = true;
                    CreateColumnMappingControls();
                }
                
                UpdateNavigationButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading CSV file: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Dictionary<string, object>> ReadCsvFile(string filePath)
        {
            var records = new List<Dictionary<string, object>>();
            
            using var reader = new StreamReader(filePath);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
            
            csv.Read();
            csv.ReadHeader();
            var headers = csv.HeaderRecord;
            
            while (csv.Read())
            {
                var record = new Dictionary<string, object>();
                foreach (var header in headers!)
                {
                    record[header] = csv.GetField(header) ?? string.Empty;
                }
                records.Add(record);
            }
            
            return records;
        }

        private DataTable ConvertToDataTable(List<Dictionary<string, object>> data)
        {
            var dataTable = new DataTable();
            
            if (data.Any())
            {
                // Add columns
                foreach (var key in data.First().Keys)
                {
                    dataTable.Columns.Add(key);
                }
                
                // Add rows
                foreach (var record in data)
                {
                    var row = dataTable.NewRow();
                    foreach (var kvp in record)
                    {
                        row[kvp.Key] = kvp.Value;
                    }
                    dataTable.Rows.Add(row);
                }
            }
            
            return dataTable;
        }

        private void CreateColumnMappingControls()
        {
            mappingPanel.Children.Clear();
            _columnMappings.Clear();
            
            if (!_csvData.Any()) return;
            
            var csvColumns = _csvData.First().Keys.ToList();
            var projectFields = new List<string>
            {
                "-- Select Field --",
                "Project Reference",
                "Project Name *",
                "Customer Name",
                "Description",
                "Team Name",
                "Equipment Type *",
                "Start Date *",
                "End Date *",
                "Status",
                "Estimated Hours",
                "Notes"
            };
            
            foreach (var csvColumn in csvColumns)
            {
                var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };
                
                var label = new TextBlock 
                { 
                    Text = csvColumn, 
                    Width = 200, 
                    VerticalAlignment = VerticalAlignment.Center,
                    FontWeight = FontWeights.SemiBold
                };
                
                var arrow = new TextBlock 
                { 
                    Text = " → ", 
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 10, 0)
                };
                
                var comboBox = new ComboBox 
                { 
                    Width = 200,
                    ItemsSource = projectFields,
                    SelectedIndex = 0,
                    Tag = csvColumn
                };
                
                comboBox.SelectionChanged += (s, e) =>
                {
                    if (s is ComboBox cb && cb.Tag is string column)
                    {
                        var selectedField = cb.SelectedItem?.ToString();
                        if (selectedField != null && selectedField != "-- Select Field --")
                        {
                            _columnMappings[column] = selectedField;
                        }
                        else
                        {
                            _columnMappings.Remove(column);
                        }
                        UpdateNavigationButtons();
                    }
                };
                
                // Try to auto-map common column names
                AutoMapColumn(csvColumn, comboBox, projectFields);
                
                panel.Children.Add(label);
                panel.Children.Add(arrow);
                panel.Children.Add(comboBox);
                
                mappingPanel.Children.Add(panel);
            }
        }

        private void AutoMapColumn(string csvColumn, ComboBox comboBox, List<string> projectFields)
        {
            var columnLower = csvColumn.ToLower().Trim();
            
            var mappings = new Dictionary<string, string>
            {
                { "projectref", "Project Reference" },
                { "project ref", "Project Reference" },
                { "project reference", "Project Reference" },
                { "project name", "Project Name *" },
                { "name", "Project Name *" },
                { "description", "Project Name *" }, // Map description to project name if no explicit project name
                { "customer name", "Customer Name" },
                { "customer", "Customer Name" },
                { "team", "Team Name" },
                { "team name", "Team Name" },
                { "manager name", "Team Name" },
                { "manager", "Team Name" },
                { "equipment", "Equipment Type *" },
                { "equipment type", "Equipment Type *" },
                { "start date", "Start Date *" },
                { "start", "Start Date *" },
                { "end date", "End Date *" },
                { "end", "End Date *" },
                { "status", "Status" },
                { "hours", "Estimated Hours" },
                { "estimated hours", "Estimated Hours" },
                { "notes", "Notes" },
                { "comments", "Notes" }
            };
            
            if (mappings.ContainsKey(columnLower))
            {
                var targetField = mappings[columnLower];
                var index = projectFields.IndexOf(targetField);
                if (index >= 0)
                {
                    comboBox.SelectedIndex = index;
                }
            }
        }

        private bool ValidateColumnMappings()
        {
            // Only require essential fields - equipment type can be set manually after import
            var requiredFields = new[] { "Project Name *", "Start Date *", "End Date *" };
            var mappedFields = _columnMappings.Values.ToList();

            return requiredFields.All(field => mappedFields.Contains(field));
        }

        private void BtnDownloadTemplate_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv",
                Title = "Save CSV Template",
                FileName = "ProjectImportTemplate.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                CreateCsvTemplate(saveFileDialog.FileName);
            }
        }

        private void CreateCsvTemplate(string filePath)
        {
            try
            {
                using var writer = new StreamWriter(filePath);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write headers
                csv.WriteField("Project Name");
                csv.WriteField("Description");
                csv.WriteField("Team Name");
                csv.WriteField("Equipment Type");
                csv.WriteField("Start Date");
                csv.WriteField("End Date");
                csv.WriteField("Status");
                csv.WriteField("Estimated Hours");
                csv.WriteField("Notes");
                csv.NextRecord();

                // Write sample data
                csv.WriteField("Sample Project 1");
                csv.WriteField("Installation of network equipment");
                csv.WriteField("Team 1");
                csv.WriteField("ISP");
                csv.WriteField("2024-01-15");
                csv.WriteField("2024-01-30");
                csv.WriteField("Not Started");
                csv.WriteField("40");
                csv.WriteField("Priority project");
                csv.NextRecord();

                csv.WriteField("Sample Project 2");
                csv.WriteField("CCTV system setup");
                csv.WriteField("Team 2");
                csv.WriteField("CCTV");
                csv.WriteField("2024-02-01");
                csv.WriteField("2024-02-15");
                csv.WriteField("In Progress");
                csv.WriteField("25");
                csv.WriteField("Customer site");
                csv.NextRecord();

                MessageBox.Show($"CSV template saved to: {filePath}", "Template Created",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating template: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnValidate_Click(object sender, RoutedEventArgs e)
        {
            ValidateImportData();
        }

        private void ValidateImportData()
        {
            _validationResults.Clear();

            for (int i = 0; i < _csvData.Count; i++)
            {
                var row = _csvData[i];
                var result = new ValidationResult
                {
                    RowNumber = i + 2, // +2 because CSV has header and is 1-based
                    Status = "Valid",
                    Issues = new List<string>()
                };

                try
                {
                    // Get mapped values
                    var projectName = GetMappedValue(row, "Project Name *");
                    var teamName = GetMappedValue(row, "Team Name *");
                    var equipmentType = GetMappedValue(row, "Equipment Type *");
                    var startDateStr = GetMappedValue(row, "Start Date *");
                    var endDateStr = GetMappedValue(row, "End Date *");

                    result.ProjectName = projectName;

                    // Validate required fields
                    if (string.IsNullOrWhiteSpace(projectName))
                        result.Issues.Add("Project name is required");

                    // Team name is optional - can be assigned manually after import
                    if (!string.IsNullOrWhiteSpace(teamName) && !_teams.Any(t => t.Name.Equals(teamName, StringComparison.OrdinalIgnoreCase)))
                        result.Issues.Add($"Team '{teamName}' not found");

                    if (string.IsNullOrWhiteSpace(equipmentType))
                        result.Issues.Add("Equipment type is required");
                    else if (!EquipmentType.All.Contains(equipmentType))
                        result.Issues.Add($"Invalid equipment type '{equipmentType}'");

                    // Validate dates
                    if (string.IsNullOrWhiteSpace(startDateStr))
                        result.Issues.Add("Start date is required");
                    else if (!DateTime.TryParse(startDateStr, out _))
                        result.Issues.Add("Invalid start date format");

                    if (string.IsNullOrWhiteSpace(endDateStr))
                        result.Issues.Add("End date is required");
                    else if (!DateTime.TryParse(endDateStr, out _))
                        result.Issues.Add("Invalid end date format");
                    else if (DateTime.TryParse(startDateStr, out var startDate) &&
                             DateTime.TryParse(endDateStr, out var endDate) &&
                             endDate < startDate)
                        result.Issues.Add("End date must be after start date");

                    // Validate estimated hours (if provided)
                    var estimatedHoursStr = GetMappedValue(row, "Estimated Hours");
                    if (!string.IsNullOrWhiteSpace(estimatedHoursStr) &&
                        (!decimal.TryParse(estimatedHoursStr, out var hours) || hours < 0))
                        result.Issues.Add("Invalid estimated hours");

                    if (result.Issues.Any())
                        result.Status = "Error";
                }
                catch (Exception ex)
                {
                    result.Status = "Error";
                    result.Issues.Add($"Validation error: {ex.Message}");
                }

                _validationResults.Add(result);
            }

            // Update UI
            validationDataGrid.ItemsSource = _validationResults;

            var validCount = _validationResults.Count(r => r.Status == "Valid");
            var errorCount = _validationResults.Count(r => r.Status == "Error");

            validationSummary.Text = $"Valid: {validCount}, Errors: {errorCount}";

            btnStartImport.IsEnabled = validCount > 0;
            tabImport.IsEnabled = true;
        }

        private string GetMappedValue(Dictionary<string, object> row, string fieldName)
        {
            var csvColumn = _columnMappings.FirstOrDefault(kvp => kvp.Value == fieldName).Key;
            if (csvColumn != null && row.ContainsKey(csvColumn))
            {
                return row[csvColumn]?.ToString()?.Trim() ?? string.Empty;
            }
            return string.Empty;
        }

        private async void BtnStartImport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                btnStartImport.IsEnabled = false;
                btnStartImport.Content = "Importing...";

                var rowsToImport = _validationResults.Where(r => r.Status == "Valid" ||
                    (chkSkipErrors.IsChecked == true && r.Status == "Error")).ToList();

                if (chkSkipErrors.IsChecked != true)
                {
                    rowsToImport = _validationResults.Where(r => r.Status == "Valid").ToList();
                }

                ImportedProjects = await Task.Run(() => CreateProjectsFromValidData(rowsToImport));

                ImportSuccessful = ImportedProjects.Any();

                if (ImportSuccessful)
                {
                    MessageBox.Show($"Successfully imported {ImportedProjects.Count} projects.",
                        "Import Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("No valid projects were imported.", "Import Failed",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during import: {ex.Message}", "Import Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnStartImport.IsEnabled = true;
                btnStartImport.Content = "Start Import";
            }
        }

        private List<Project> CreateProjectsFromValidData(List<ValidationResult> validRows)
        {
            var projects = new List<Project>();

            foreach (var validationResult in validRows.Where(r => r.Status == "Valid"))
            {
                try
                {
                    var rowIndex = validationResult.RowNumber - 2; // Convert back to 0-based index
                    var row = _csvData[rowIndex];

                    var projectName = GetMappedValue(row, "Project Name *");
                    var description = GetMappedValue(row, "Description");

                    // If description was mapped to project name, use it and clear description
                    if (string.IsNullOrEmpty(projectName) && !string.IsNullOrEmpty(description))
                    {
                        projectName = description;
                        description = null;
                    }

                    var project = new Project
                    {
                        ProjectRef = GetMappedValue(row, "Project Reference"),
                        Name = projectName,
                        CustomerName = GetMappedValue(row, "Customer Name"),
                        Description = description,
                        EquipmentType = GetMappedValue(row, "Equipment Type *") ?? "Other", // Default to "Other" if not specified
                        StartDate = DateTime.Parse(GetMappedValue(row, "Start Date *")),
                        EndDate = DateTime.Parse(GetMappedValue(row, "End Date *")),
                        Status = GetMappedValue(row, "Status") ?? ProjectStatus.NotStarted,
                        Notes = GetMappedValue(row, "Notes"),
                        CreatedDate = DateTime.Now,
                        TeamId = 1 // Default to Team 1, can be changed manually after import
                    };

                    // Try to find team by name if provided
                    var teamName = GetMappedValue(row, "Team Name");
                    if (!string.IsNullOrWhiteSpace(teamName))
                    {
                        var team = _teams.FirstOrDefault(t => t.Name.Equals(teamName, StringComparison.OrdinalIgnoreCase));
                        if (team != null)
                        {
                            project.TeamId = team.Id;
                        }
                    }

                    // Parse estimated hours
                    var estimatedHoursStr = GetMappedValue(row, "Estimated Hours");
                    if (!string.IsNullOrWhiteSpace(estimatedHoursStr) &&
                        decimal.TryParse(estimatedHoursStr, out var hours))
                    {
                        project.EstimatedHours = hours;
                    }

                    projects.Add(project);
                }
                catch (Exception ex)
                {
                    // Log error but continue with other projects
                    System.Diagnostics.Debug.WriteLine($"Error creating project from row {validationResult.RowNumber}: {ex.Message}");
                }
            }

            return projects;
        }

        private void BtnPrevious_Click(object sender, RoutedEventArgs e)
        {
            if (importTabControl.SelectedIndex > 0)
            {
                importTabControl.SelectedIndex--;
            }
        }

        private void BtnNext_Click(object sender, RoutedEventArgs e)
        {
            if (importTabControl.SelectedIndex == 2) // Import tab
            {
                // Perform the import
                BtnStartImport_Click(sender, e);
            }
            else if (importTabControl.SelectedIndex < importTabControl.Items.Count - 1)
            {
                importTabControl.SelectedIndex++;
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class ValidationResult
    {
        public int RowNumber { get; set; }
        public string Status { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public List<string> Issues { get; set; } = new();
        public string IssuesText => string.Join("; ", Issues);
    }
}
