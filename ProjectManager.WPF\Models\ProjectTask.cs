using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectManager.WPF.Models
{
    public class ProjectTask
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Not Started";

        [Column(TypeName = "decimal(10,2)")]
        public decimal? EstimatedHours { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? ActualHours { get; set; }

        [StringLength(2000)]
        public string? Notes { get; set; }

        public int Priority { get; set; } = 1; // 1 = Low, 2 = Medium, 3 = High

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? ModifiedDate { get; set; }

        // Foreign Keys
        public int ProjectId { get; set; }
        public int? TeamId { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual Team? Team { get; set; }
    }
}
