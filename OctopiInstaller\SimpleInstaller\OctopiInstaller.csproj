<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>octopi.ico</ApplicationIcon>
    <AssemblyTitle>Octopi Project Manager Installer</AssemblyTitle>
    <AssemblyDescription>Professional installer for Octopi Project Manager</AssemblyDescription>
    <AssemblyCompany><PERSON> (AntmanZA)</AssemblyCompany>
    <AssemblyProduct>Octopi Project Manager Installer</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025 AntmanZa</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="..\..\OctopiProjectManager_v1.1_Fixed_Deployment\ProjectManager.WPF.exe">
      <LogicalName>OctopiProjectManagerInstaller.ProjectManager.WPF.exe</LogicalName>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\ProjectManager.WPF\octopi.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
