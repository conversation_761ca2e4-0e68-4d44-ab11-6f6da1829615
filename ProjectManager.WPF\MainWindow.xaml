﻿<Window x:Class="ProjectManager.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ProjectManager.WPF"
        xmlns:controls="clr-namespace:ProjectManager.WPF.Controls"
        mc:Ignorable="d"
        Title="Octopi Project Manager - Equipment Installation Tracker"
        Height="900" Width="1400"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Icon="octopi.ico">

    <Window.Resources>
        <!-- Define styles for consistent UI -->
        <Style x:Key="TabItemStyle" TargetType="TabItem">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="2,0"/>
        </Style>

        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="{DynamicResource AppSurfaceBrush}" BorderBrush="{DynamicResource AppBorderBrush}" BorderThickness="0,0,0,1">
            <MenuItem Header="_Help">
                <MenuItem Header="_About Octopi Project Manager" Click="MenuAbout_Click">
                    <MenuItem.Icon>
                        <TextBlock Text="🐙" FontSize="14"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
        </Menu>

        <!-- Header -->
        <Border Grid.Row="1" Background="{DynamicResource AppPrimaryBrush}" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🐙"
                               FontSize="28"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="Octopi Project Manager"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="Equipment Installation Tracker"
                               FontSize="14"
                               Foreground="#D8DEE9"
                               VerticalAlignment="Center"
                               Margin="15,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="btnRefresh"
                            Content="🔄 Refresh"
                            Style="{StaticResource ButtonStyle}"
                            Background="{DynamicResource AppSecondaryBrush}"
                            Foreground="White"
                            BorderBrush="Transparent"
                            Click="BtnRefresh_Click"/>
                    <Button Name="btnSettings"
                            Content="⚙️ Settings"
                            Style="{StaticResource ButtonStyle}"
                            Background="{DynamicResource AppSecondaryBrush}"
                            Foreground="White"
                            BorderBrush="Transparent"
                            Click="BtnSettings_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area with Tabs -->
        <TabControl Grid.Row="2" Name="mainTabControl"
                    Background="{DynamicResource AppSurfaceBrush}"
                    BorderBrush="{DynamicResource AppBorderBrush}"
                    BorderThickness="0,1,0,0">

            <!-- Projects Tab -->
            <TabItem Header="📋 Projects" Style="{StaticResource TabItemStyle}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                               Text="Project Management"
                               Style="{StaticResource HeaderTextStyle}"/>

                    <!-- Project Controls -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Name="btnAddProject"
                                Content="➕ Add Project"
                                Style="{StaticResource ButtonStyle}"
                                Background="#A3BE8C"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnAddProject_Click"/>
                        <Button Name="btnEditProject"
                                Content="✏️ Edit Project"
                                Style="{StaticResource ButtonStyle}"
                                Background="#EBCB8B"
                                Foreground="Black"
                                BorderBrush="Transparent"
                                Click="BtnEditProject_Click"/>
                        <Button Name="btnDeleteProject"
                                Content="🗑️ Delete Project"
                                Style="{StaticResource ButtonStyle}"
                                Background="#BF616A"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnDeleteProject_Click"/>
                        <Separator Width="20" Background="Transparent"/>
                        <Button Name="btnImportCSV"
                                Content="📁 Import Excel/CSV"
                                Style="{StaticResource ButtonStyle}"
                                Background="#88C0D0"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnImportCSV_Click"/>
                        <Button Name="btnExportCSV"
                                Content="💾 Export CSV"
                                Style="{StaticResource ButtonStyle}"
                                Background="#81A1C1"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnExportCSV_Click"/>
                    </StackPanel>

                    <!-- Projects DataGrid -->
                    <DataGrid Grid.Row="2"
                              Name="projectsDataGrid"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              SelectionMode="Single"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA"
                              RowBackground="White"
                              BorderBrush="#E5E9F0"
                              BorderThickness="1">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Project Ref" Binding="{Binding ProjectRef}" Width="100"/>
                            <DataGridTextColumn Header="Project Name" Binding="{Binding Name}" Width="180"/>
                            <DataGridTextColumn Header="Customer" Binding="{Binding CustomerName}" Width="150"/>
                            <DataGridTextColumn Header="Team" Binding="{Binding Team.Name}" Width="100"/>
                            <DataGridTextColumn Header="Equipment Type" Binding="{Binding EquipmentType}" Width="120"/>
                            <DataGridTextColumn Header="Start Date" Binding="{Binding StartDate, StringFormat='{}{0:MM/dd/yyyy}'}" Width="100"/>
                            <DataGridTextColumn Header="End Date" Binding="{Binding EndDate, StringFormat='{}{0:MM/dd/yyyy}'}" Width="100"/>
                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="120"/>
                            <DataGridTextColumn Header="Est. Hours" Binding="{Binding EstimatedHours}" Width="100"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Gantt Chart Tab -->
            <TabItem Header="📊 Gantt Chart" Style="{StaticResource TabItemStyle}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                               Text="Project Timeline Visualization"
                               Style="{StaticResource HeaderTextStyle}"/>

                    <!-- Gantt Chart Controls -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="Filter by Team:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox Name="cmbTeamFilter" Width="150" Margin="0,0,10,0"/>
                        <TextBlock Text="Equipment Type:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox Name="cmbEquipmentFilter" Width="150" Margin="0,0,10,0"/>
                        <Button Name="btnApplyFilters"
                                Content="Apply Filters"
                                Style="{StaticResource ButtonStyle}"
                                Background="#5E81AC"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnApplyFilters_Click"/>
                        <Button Name="btnClearFilters"
                                Content="Clear Filters"
                                Style="{StaticResource ButtonStyle}"
                                Background="#4C566A"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnClearFilters_Click"/>
                    </StackPanel>

                    <!-- Gantt Chart -->
                    <Border Grid.Row="2"
                            BorderBrush="#E5E9F0"
                            BorderThickness="1"
                            Background="White">
                        <controls:GanttChart x:Name="ganttChart" ProjectClicked="GanttChart_ProjectClicked"/>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Reports Tab -->
            <TabItem Header="📈 Reports" Style="{StaticResource TabItemStyle}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                               Text="Reports and Analytics"
                               Style="{StaticResource HeaderTextStyle}"/>

                    <!-- Report Controls -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Name="btnProjectSummary"
                                Content="📋 Project Summary"
                                Style="{StaticResource ButtonStyle}"
                                Background="#A3BE8C"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnProjectSummary_Click"/>
                        <Button Name="btnTeamWorkload"
                                Content="👥 Team Workload"
                                Style="{StaticResource ButtonStyle}"
                                Background="#EBCB8B"
                                Foreground="Black"
                                BorderBrush="Transparent"
                                Click="BtnTeamWorkload_Click"/>
                        <Button Name="btnEquipmentBreakdown"
                                Content="🔧 Equipment Breakdown"
                                Style="{StaticResource ButtonStyle}"
                                Background="#88C0D0"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnEquipmentBreakdown_Click"/>
                        <Separator Width="20" Background="Transparent"/>
                        <Button Name="btnExportPDF"
                                Content="📄 Export PDF"
                                Style="{StaticResource ButtonStyle}"
                                Background="#5E81AC"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnExportPDF_Click"/>
                        <Button Name="btnExportExcel"
                                Content="📊 Export Excel"
                                Style="{StaticResource ButtonStyle}"
                                Background="#81A1C1"
                                Foreground="White"
                                BorderBrush="Transparent"
                                Click="BtnExportExcel_Click"/>
                    </StackPanel>

                    <!-- Reports Content -->
                    <TabControl Grid.Row="2" Name="reportsTabControl">
                        <TabItem Header="Summary">
                            <DataGrid Name="summaryDataGrid"
                                      AutoGenerateColumns="True"
                                      IsReadOnly="True"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      AlternatingRowBackground="#F8F9FA"
                                      RowBackground="White"
                                      BorderBrush="#E5E9F0"
                                      BorderThickness="1"/>
                        </TabItem>
                        <TabItem Header="Charts">
                            <Grid>
                                <TextBlock Text="Charts and visualizations will be implemented here"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="16"
                                           Foreground="#4C566A"/>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </TabItem>

            <!-- Settings Tab -->
            <TabItem Header="⚙️ Settings" Style="{StaticResource TabItemStyle}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                               Text="Application Settings"
                               Style="{StaticResource HeaderTextStyle}"/>

                    <TabControl Grid.Row="1" Name="settingsTabControl">
                        <TabItem Header="Teams">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                    <Button Name="btnAddTeam"
                                            Content="➕ Add Team"
                                            Style="{StaticResource ButtonStyle}"
                                            Background="#A3BE8C"
                                            Foreground="White"
                                            BorderBrush="Transparent"
                                            Click="BtnAddTeam_Click"/>
                                    <Button Name="btnEditTeam"
                                            Content="✏️ Edit Team"
                                            Style="{StaticResource ButtonStyle}"
                                            Background="#EBCB8B"
                                            Foreground="Black"
                                            BorderBrush="Transparent"
                                            Click="BtnEditTeam_Click"/>
                                    <Button Name="btnDeleteTeam"
                                            Content="🗑️ Delete Team"
                                            Style="{StaticResource ButtonStyle}"
                                            Background="#BF616A"
                                            Foreground="White"
                                            BorderBrush="Transparent"
                                            Click="BtnDeleteTeam_Click"/>
                                </StackPanel>

                                <DataGrid Grid.Row="1"
                                          Name="teamsDataGrid"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          SelectionMode="Single"
                                          GridLinesVisibility="Horizontal"
                                          HeadersVisibility="Column"
                                          AlternatingRowBackground="#F8F9FA"
                                          RowBackground="White"
                                          BorderBrush="#E5E9F0"
                                          BorderThickness="1">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50" IsReadOnly="True"/>
                                        <DataGridTextColumn Header="Team Name" Binding="{Binding Name}" Width="200"/>
                                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                                        <DataGridCheckBoxColumn Header="Active" Binding="{Binding IsActive}" Width="80"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>
                        <TabItem Header="Preferences">
                            <Grid Margin="10">
                                <StackPanel>
                                    <TextBlock Text="Application Preferences" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <CheckBox Name="chkDarkMode" Content="Enable Dark Mode" Margin="0,5"/>
                                    <CheckBox Name="chkAutoSave" Content="Auto-save changes" Margin="0,5"/>
                                    <CheckBox Name="chkShowNotifications" Content="Show notifications" Margin="0,5"/>

                                    <TextBlock Text="Default Settings" FontWeight="Bold" Margin="0,20,0,10"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,5">
                                        <TextBlock Text="Default Equipment Type:" Width="150" VerticalAlignment="Center"/>
                                        <ComboBox Name="cmbDefaultEquipmentType" Width="150"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,5">
                                        <TextBlock Text="Default Project Status:" Width="150" VerticalAlignment="Center"/>
                                        <ComboBox Name="cmbDefaultProjectStatus" Width="150"/>
                                    </StackPanel>

                                    <Button Name="btnSaveSettings"
                                            Content="💾 Save Settings"
                                            Style="{StaticResource ButtonStyle}"
                                            Background="#5E81AC"
                                            Foreground="White"
                                            BorderBrush="Transparent"
                                            HorizontalAlignment="Left"
                                            Margin="0,20,0,0"
                                            Click="BtnSaveSettings_Click"/>
                                </StackPanel>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Background="{DynamicResource AppPrimaryBrush}" Foreground="White">
            <StatusBarItem>
                <TextBlock Name="statusText" Text="Ready"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="projectCountText" Text="Projects: 0"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="teamCountText" Text="Teams: 0"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="lastUpdatedText" Text="Last Updated: Never"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
