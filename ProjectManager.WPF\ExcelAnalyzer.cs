using System;
using System.IO;
using System.Threading.Tasks;
using ProjectManager.WPF.Services;

namespace ProjectManager.WPF
{
    public class ExcelAnalyzer
    {
        public static async Task AnalyzeExampleFile()
        {
            var excelService = new ExcelService();
            var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ExampleProjectListing.xlsx");
            
            if (!File.Exists(filePath))
            {
                Console.WriteLine($"Example file not found at: {filePath}");
                return;
            }
            
            Console.WriteLine("Analyzing Excel file structure...");
            Console.WriteLine($"File: {filePath}");
            Console.WriteLine();
            
            var result = await excelService.AnalyzeExcelFileAsync(filePath);
            
            if (!result.IsSuccess)
            {
                Console.WriteLine($"Error: {result.ErrorMessage}");
                return;
            }
            
            Console.WriteLine($"Worksheet: {result.WorksheetName}");
            Console.WriteLine($"Dimensions: {result.TotalRows} rows x {result.TotalColumns} columns");
            Console.WriteLine($"Range: Row {result.StartRow}-{result.EndRow}, Column {result.StartColumn}-{result.EndColumn}");
            Console.WriteLine();
            
            Console.WriteLine("Headers:");
            for (int i = 0; i < result.Headers.Count; i++)
            {
                Console.WriteLine($"  Column {i + 1}: {result.Headers[i]}");
            }
            Console.WriteLine();
            
            Console.WriteLine("Sample Data (first 5 rows):");
            for (int row = 0; row < result.SampleData.Count; row++)
            {
                Console.WriteLine($"  Row {row + 2}:");
                for (int col = 0; col < result.SampleData[row].Count; col++)
                {
                    var value = result.SampleData[row][col];
                    if (!string.IsNullOrEmpty(value))
                    {
                        Console.WriteLine($"    {result.Headers[col]}: {value}");
                    }
                }
                Console.WriteLine();
            }
        }
    }
}
