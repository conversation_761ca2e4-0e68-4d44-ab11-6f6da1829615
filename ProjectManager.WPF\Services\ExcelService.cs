using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;

namespace ProjectManager.WPF.Services
{
    public class ExcelService
    {
        static ExcelService()
        {
            // Set EPPlus license for non-commercial use (EPPlus 8.0+)
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<ExcelAnalysisResult> AnalyzeExcelFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                var result = new ExcelAnalysisResult();
                
                try
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    
                    if (package.Workbook.Worksheets.Count == 0)
                    {
                        result.ErrorMessage = "No worksheets found in the Excel file.";
                        return result;
                    }
                    
                    var worksheet = package.Workbook.Worksheets[0]; // Use first worksheet
                    result.WorksheetName = worksheet.Name;
                    
                    // Get dimensions
                    var start = worksheet.Dimension?.Start;
                    var end = worksheet.Dimension?.End;
                    
                    if (start == null || end == null)
                    {
                        result.ErrorMessage = "Worksheet appears to be empty.";
                        return result;
                    }
                    
                    result.StartRow = start.Row;
                    result.StartColumn = start.Column;
                    result.EndRow = end.Row;
                    result.EndColumn = end.Column;
                    result.TotalRows = end.Row - start.Row + 1;
                    result.TotalColumns = end.Column - start.Column + 1;
                    
                    // Read headers (assuming first row contains headers)
                    result.Headers = new List<string>();
                    for (int col = start.Column; col <= end.Column; col++)
                    {
                        var headerValue = worksheet.Cells[start.Row, col].Value?.ToString() ?? $"Column{col}";
                        result.Headers.Add(headerValue);
                    }
                    
                    // Read sample data (first 5 rows after header)
                    result.SampleData = new List<List<string>>();
                    int sampleRows = Math.Min(5, end.Row - start.Row);
                    
                    for (int row = start.Row + 1; row <= start.Row + sampleRows; row++)
                    {
                        var rowData = new List<string>();
                        for (int col = start.Column; col <= end.Column; col++)
                        {
                            var cellValue = worksheet.Cells[row, col].Value?.ToString() ?? "";
                            rowData.Add(cellValue);
                        }
                        result.SampleData.Add(rowData);
                    }
                    
                    result.IsSuccess = true;
                }
                catch (Exception ex)
                {
                    result.ErrorMessage = $"Error analyzing Excel file: {ex.Message}";
                }
                
                return result;
            });
        }

        public async Task<List<Dictionary<string, object>>> ReadExcelDataAsync(string filePath, int headerRow = 1, int startDataRow = 2)
        {
            return await Task.Run(() =>
            {
                var data = new List<Dictionary<string, object>>();
                
                try
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets[0];
                    
                    if (worksheet.Dimension == null)
                        return data;
                    
                    var start = worksheet.Dimension.Start;
                    var end = worksheet.Dimension.End;
                    
                    // Read headers
                    var headers = new List<string>();
                    for (int col = start.Column; col <= end.Column; col++)
                    {
                        var headerValue = worksheet.Cells[headerRow, col].Value?.ToString() ?? $"Column{col}";
                        headers.Add(headerValue);
                    }
                    
                    // Read data rows
                    for (int row = startDataRow; row <= end.Row; row++)
                    {
                        var rowData = new Dictionary<string, object>();
                        bool hasData = false;
                        
                        for (int col = start.Column; col <= end.Column; col++)
                        {
                            var cellValue = worksheet.Cells[row, col].Value;
                            var headerName = headers[col - start.Column];
                            
                            if (cellValue != null)
                            {
                                hasData = true;
                                rowData[headerName] = cellValue;
                            }
                            else
                            {
                                rowData[headerName] = "";
                            }
                        }
                        
                        // Only add rows that have at least some data
                        if (hasData)
                        {
                            data.Add(rowData);
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error reading Excel data: {ex.Message}");
                }
                
                return data;
            });
        }

        public async Task<string> ConvertExcelToCsvAsync(string excelFilePath, string csvFilePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var package = new ExcelPackage(new FileInfo(excelFilePath));
                    var worksheet = package.Workbook.Worksheets[0];
                    
                    if (worksheet.Dimension == null)
                        return "Worksheet is empty";
                    
                    using var writer = new StreamWriter(csvFilePath);
                    var start = worksheet.Dimension.Start;
                    var end = worksheet.Dimension.End;
                    
                    for (int row = start.Row; row <= end.Row; row++)
                    {
                        var values = new List<string>();
                        for (int col = start.Column; col <= end.Column; col++)
                        {
                            var cellValue = worksheet.Cells[row, col].Value?.ToString() ?? "";
                            // Escape commas and quotes in CSV
                            if (cellValue.Contains(",") || cellValue.Contains("\"") || cellValue.Contains("\n"))
                            {
                                cellValue = "\"" + cellValue.Replace("\"", "\"\"") + "\"";
                            }
                            values.Add(cellValue);
                        }
                        writer.WriteLine(string.Join(",", values));
                    }
                    
                    return "Success";
                }
                catch (Exception ex)
                {
                    return $"Error: {ex.Message}";
                }
            });
        }
    }

    public class ExcelAnalysisResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string WorksheetName { get; set; } = string.Empty;
        public int StartRow { get; set; }
        public int StartColumn { get; set; }
        public int EndRow { get; set; }
        public int EndColumn { get; set; }
        public int TotalRows { get; set; }
        public int TotalColumns { get; set; }
        public List<string> Headers { get; set; } = new();
        public List<List<string>> SampleData { get; set; } = new();
    }
}
