@echo off
echo.
echo ========================================
echo   Octopi Project Manager Uninstaller
echo   Developed by <PERSON> (AntmanZA)
echo ========================================
echo.

echo Removing Octopi Project Manager...
echo.

REM Remove desktop shortcut
if exist "%USERPROFILE%\Desktop\Octopi Project Manager.lnk" (
    del "%USERPROFILE%\Desktop\Octopi Project Manager.lnk"
    echo Desktop shortcut removed.
)

REM Remove Start Menu shortcut
if exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager" (
    rmdir /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager"
    echo Start Menu shortcut removed.
)

REM Remove program files
if exist "%ProgramFiles%\OctopiProjectManager" (
    rmdir /s /q "%ProgramFiles%\OctopiProjectManager"
    echo Program files removed.
)

echo.
echo ========================================
echo Uninstallation completed successfully!
echo.
echo Note: User data and settings are preserved.
echo If you want to remove all data, manually delete:
echo %APPDATA%\OctopiProjectManager (if exists)
echo ========================================
echo.
pause
