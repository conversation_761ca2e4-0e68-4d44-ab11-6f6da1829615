using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ProjectManager.WPF.Models;
using ProjectManager.WPF.Services;
using ProjectManager.WPF.Views;

namespace ProjectManager.WPF
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private List<Project> _projects = new();
        private List<Team> _teams = new();

        public MainWindow(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await InitializeDataAsync();
            SetupComboBoxes();
            UpdateStatusBar();
        }

        private async Task InitializeDataAsync()
        {
            try
            {
                statusText.Text = "Loading data...";

                // Load teams and projects
                _teams = await _databaseService.GetAllTeamsAsync();
                _projects = await _databaseService.GetAllProjectsAsync();

                // Bind data to grids
                projectsDataGrid.ItemsSource = _projects;
                teamsDataGrid.ItemsSource = _teams;

                statusText.Text = "Data loaded successfully";
            }
            catch (Exception ex)
            {
                statusText.Text = "Error loading data";
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupComboBoxes()
        {
            // Setup team filters
            var allTeamsOption = new Team { Id = 0, Name = "All Teams" };
            var teamOptions = new List<Team> { allTeamsOption };
            teamOptions.AddRange(_teams);
            cmbTeamFilter.ItemsSource = teamOptions;
            cmbTeamFilter.DisplayMemberPath = "Name";
            cmbTeamFilter.SelectedIndex = 0;

            // Setup equipment type filters
            var allEquipmentOption = "All Types";
            var equipmentOptions = new List<string> { allEquipmentOption };
            equipmentOptions.AddRange(EquipmentType.All);
            cmbEquipmentFilter.ItemsSource = equipmentOptions;
            cmbEquipmentFilter.SelectedIndex = 0;

            // Setup default settings
            cmbDefaultEquipmentType.ItemsSource = EquipmentType.All;
            cmbDefaultEquipmentType.SelectedIndex = 0;

            cmbDefaultProjectStatus.ItemsSource = ProjectStatus.All;
            cmbDefaultProjectStatus.SelectedIndex = 0;
        }

        private void UpdateStatusBar()
        {
            projectCountText.Text = $"Projects: {_projects.Count}";
            teamCountText.Text = $"Teams: {_teams.Count}";
            lastUpdatedText.Text = $"Last Updated: {DateTime.Now:HH:mm:ss}";
        }

        // Event Handlers
        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await InitializeDataAsync();
            UpdateStatusBar();
        }

        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            mainTabControl.SelectedIndex = 3; // Settings tab
        }

        private async void BtnAddProject_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectFormDialog(_teams);
            if (dialog.ShowDialog() == true && dialog.Project != null)
            {
                try
                {
                    await _databaseService.AddProjectAsync(dialog.Project);
                    await InitializeDataAsync();
                    UpdateStatusBar();
                    statusText.Text = "Project added successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding project: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnEditProject_Click(object sender, RoutedEventArgs e)
        {
            if (projectsDataGrid.SelectedItem is Project selectedProject)
            {
                // TODO: Open Edit Project dialog
                MessageBox.Show($"Edit Project '{selectedProject.Name}' functionality will be implemented in the next phase.",
                    "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("Please select a project to edit.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void BtnDeleteProject_Click(object sender, RoutedEventArgs e)
        {
            if (projectsDataGrid.SelectedItem is Project selectedProject)
            {
                var result = MessageBox.Show($"Are you sure you want to delete project '{selectedProject.Name}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _databaseService.DeleteProjectAsync(selectedProject.Id);
                        await InitializeDataAsync();
                        UpdateStatusBar();
                        statusText.Text = "Project deleted successfully";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting project: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a project to delete.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void BtnImportCSV_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                Title = "Select CSV file to import"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                // TODO: Implement CSV import
                MessageBox.Show($"CSV Import from '{openFileDialog.FileName}' will be implemented in the next phase.",
                    "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnExportCSV_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                Title = "Export projects to CSV",
                FileName = $"Projects_Export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                // TODO: Implement CSV export
                MessageBox.Show($"CSV Export to '{saveFileDialog.FileName}' will be implemented in the next phase.",
                    "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // Gantt Chart Event Handlers
        private void BtnApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement Gantt chart filtering
            MessageBox.Show("Gantt chart filtering will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
        {
            cmbTeamFilter.SelectedIndex = 0;
            cmbEquipmentFilter.SelectedIndex = 0;
            // TODO: Refresh Gantt chart
        }

        // Reports Event Handlers
        private void BtnProjectSummary_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var summary = _projects.Select(p => new
                {
                    p.Id,
                    p.Name,
                    Team = p.Team?.Name ?? "Unassigned",
                    p.EquipmentType,
                    p.Status,
                    p.StartDate,
                    p.EndDate,
                    p.EstimatedHours,
                    TaskCount = p.Tasks?.Count ?? 0
                }).ToList();

                summaryDataGrid.ItemsSource = summary;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Project summary report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating project summary: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnTeamWorkload_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var workload = _teams.Select(t => new
                {
                    TeamName = t.Name,
                    ProjectCount = _projects.Count(p => p.TeamId == t.Id),
                    TotalEstimatedHours = _projects.Where(p => p.TeamId == t.Id).Sum(p => p.EstimatedHours ?? 0),
                    InProgressProjects = _projects.Count(p => p.TeamId == t.Id && p.Status == ProjectStatus.InProgress),
                    CompletedProjects = _projects.Count(p => p.TeamId == t.Id && p.Status == ProjectStatus.Completed)
                }).ToList();

                summaryDataGrid.ItemsSource = workload;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Team workload report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating team workload report: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEquipmentBreakdown_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var breakdown = _projects.GroupBy(p => p.EquipmentType).Select(g => new
                {
                    EquipmentType = g.Key,
                    ProjectCount = g.Count(),
                    TotalEstimatedHours = g.Sum(p => p.EstimatedHours ?? 0),
                    InProgressCount = g.Count(p => p.Status == ProjectStatus.InProgress),
                    CompletedCount = g.Count(p => p.Status == ProjectStatus.Completed),
                    NotStartedCount = g.Count(p => p.Status == ProjectStatus.NotStarted)
                }).ToList();

                summaryDataGrid.ItemsSource = breakdown;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Equipment breakdown report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating equipment breakdown report: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement PDF export
            MessageBox.Show("PDF export functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement Excel export
            MessageBox.Show("Excel export functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Team Management Event Handlers
        private void BtnAddTeam_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open Add Team dialog
            MessageBox.Show("Add Team functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnEditTeam_Click(object sender, RoutedEventArgs e)
        {
            if (teamsDataGrid.SelectedItem is Team selectedTeam)
            {
                // TODO: Open Edit Team dialog
                MessageBox.Show($"Edit Team '{selectedTeam.Name}' functionality will be implemented in the next phase.",
                    "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("Please select a team to edit.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void BtnDeleteTeam_Click(object sender, RoutedEventArgs e)
        {
            if (teamsDataGrid.SelectedItem is Team selectedTeam)
            {
                // Check if team has projects
                var teamProjects = _projects.Where(p => p.TeamId == selectedTeam.Id).ToList();
                if (teamProjects.Any())
                {
                    MessageBox.Show($"Cannot delete team '{selectedTeam.Name}' because it has {teamProjects.Count} assigned project(s).",
                        "Cannot Delete", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"Are you sure you want to delete team '{selectedTeam.Name}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _databaseService.DeleteTeamAsync(selectedTeam.Id);
                        await InitializeDataAsync();
                        SetupComboBoxes();
                        UpdateStatusBar();
                        statusText.Text = "Team deleted successfully";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting team: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a team to delete.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void BtnSaveSettings_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement settings save
            MessageBox.Show("Settings save functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
