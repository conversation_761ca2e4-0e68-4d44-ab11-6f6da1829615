﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using CsvHelper;
using Microsoft.Win32;
using ProjectManager.WPF.Models;
using ProjectManager.WPF.Services;
using ProjectManager.WPF.Views;

namespace ProjectManager.WPF
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly SettingsService _settingsService;
        private ExcelService? _excelService;
        private List<Project> _projects = new();
        private List<Team> _teams = new();

        public MainWindow(DatabaseService databaseService, SettingsService settingsService)
        {
            _databaseService = databaseService;
            _settingsService = settingsService;
            // _excelService = new ExcelService(); // Temporarily disabled
            InitializeComponent();
            Loaded += MainWindow_Loaded;

            // Subscribe to theme changes
            ThemeManager.Instance.ThemeChanged += OnThemeChanged;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await InitializeDataAsync();
            SetupComboBoxes();
            LoadSettings();
            UpdateStatusBar();
        }

        private void OnThemeChanged(object? sender, bool isDarkMode)
        {
            // Update Gantt chart colors when theme changes
            if (_projects.Any())
            {
                ganttChart.LoadProjects(_projects);
            }
        }

        private async Task InitializeDataAsync()
        {
            try
            {
                statusText.Text = "Loading data...";

                // Load teams and projects
                _teams = await _databaseService.GetAllTeamsAsync();
                _projects = await _databaseService.GetAllProjectsAsync();

                // Bind data to grids
                projectsDataGrid.ItemsSource = _projects;
                teamsDataGrid.ItemsSource = _teams;

                // Load Gantt chart
                ganttChart.LoadProjects(_projects);

                statusText.Text = "Data loaded successfully";
            }
            catch (Exception ex)
            {
                statusText.Text = "Error loading data";
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupComboBoxes()
        {
            // Setup team filters
            var allTeamsOption = new Team { Id = 0, Name = "All Teams" };
            var teamOptions = new List<Team> { allTeamsOption };
            teamOptions.AddRange(_teams);
            cmbTeamFilter.ItemsSource = teamOptions;
            cmbTeamFilter.DisplayMemberPath = "Name";
            cmbTeamFilter.SelectedIndex = 0;

            // Setup equipment type filters
            var allEquipmentOption = "All Types";
            var equipmentOptions = new List<string> { allEquipmentOption };
            equipmentOptions.AddRange(EquipmentType.All);
            cmbEquipmentFilter.ItemsSource = equipmentOptions;
            cmbEquipmentFilter.SelectedIndex = 0;

            // Setup default settings
            cmbDefaultEquipmentType.ItemsSource = EquipmentType.All;
            cmbDefaultEquipmentType.SelectedIndex = 0;

            cmbDefaultProjectStatus.ItemsSource = ProjectStatus.All;
            cmbDefaultProjectStatus.SelectedIndex = 0;
        }

        private void LoadSettings()
        {
            var settings = _settingsService.Settings;

            // Load theme setting
            chkDarkMode.IsChecked = settings.DarkMode;

            // Load other settings
            chkAutoSave.IsChecked = settings.AutoSave;
            chkShowNotifications.IsChecked = settings.ShowNotifications;

            // Load default values
            if (!string.IsNullOrEmpty(settings.DefaultEquipmentType))
            {
                cmbDefaultEquipmentType.SelectedItem = settings.DefaultEquipmentType;
            }

            if (!string.IsNullOrEmpty(settings.DefaultProjectStatus))
            {
                cmbDefaultProjectStatus.SelectedItem = settings.DefaultProjectStatus;
            }
        }

        private void UpdateStatusBar()
        {
            var selectedCount = projectsDataGrid?.SelectedItems.Count ?? 0;
            var projectText = selectedCount > 0
                ? $"Projects: {_projects.Count} ({selectedCount} selected)"
                : $"Projects: {_projects.Count}";

            projectCountText.Text = projectText;
            teamCountText.Text = $"Teams: {_teams.Count}";
            lastUpdatedText.Text = $"Last Updated: {DateTime.Now:HH:mm:ss}";
        }

        // Event Handlers
        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await InitializeDataAsync();
            UpdateStatusBar();
        }

        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            mainTabControl.SelectedIndex = 3; // Settings tab
        }

        private async void BtnAddProject_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectFormDialog(_teams);
            if (dialog.ShowDialog() == true && dialog.Project != null)
            {
                try
                {
                    await _databaseService.AddProjectAsync(dialog.Project);
                    await InitializeDataAsync();
                    UpdateStatusBar();
                    statusText.Text = "Project added successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding project: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnEditProject_Click(object sender, RoutedEventArgs e)
        {
            if (projectsDataGrid.SelectedItem is Project selectedProject)
            {
                var dialog = new ProjectFormDialog(_teams, selectedProject);
                if (dialog.ShowDialog() == true && dialog.Project != null)
                {
                    try
                    {
                        await _databaseService.UpdateProjectAsync(dialog.Project);
                        await InitializeDataAsync();
                        UpdateStatusBar();
                        statusText.Text = "Project updated successfully";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error updating project: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a project to edit.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void BtnDeleteProject_Click(object sender, RoutedEventArgs e)
        {
            var selectedProjects = projectsDataGrid.SelectedItems.Cast<Project>().ToList();

            if (!selectedProjects.Any())
            {
                MessageBox.Show("Please select one or more projects to delete.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var projectNames = selectedProjects.Count == 1
                ? $"'{selectedProjects[0].Name}'"
                : $"{selectedProjects.Count} projects";

            var result = MessageBox.Show($"Are you sure you want to delete {projectNames}?\n\nThis action cannot be undone.",
                "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    statusText.Text = $"Deleting {selectedProjects.Count} project(s)...";

                    foreach (var project in selectedProjects)
                    {
                        await _databaseService.DeleteProjectAsync(project.Id);
                    }

                    await InitializeDataAsync();
                    UpdateStatusBar();
                    statusText.Text = $"Successfully deleted {selectedProjects.Count} project(s)";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting projects: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    statusText.Text = "Delete operation failed";
                }
            }
        }

        private void ProjectsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Enable/disable delete button based on selection
            btnDeleteProject.IsEnabled = projectsDataGrid.SelectedItems.Count > 0;

            // Update button text based on selection count
            var selectedCount = projectsDataGrid.SelectedItems.Count;
            if (selectedCount == 0)
            {
                btnDeleteProject.Content = "🗑️ Delete Selected";
            }
            else if (selectedCount == 1)
            {
                btnDeleteProject.Content = "🗑️ Delete Project";
            }
            else
            {
                btnDeleteProject.Content = $"🗑️ Delete {selectedCount} Projects";
            }

            // Update status bar with selection info
            UpdateStatusBar();
        }

        private void ProjectsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // Double-click to edit project (existing functionality)
            BtnEditProject_Click(sender, new RoutedEventArgs());
        }

        private void BtnSelectAll_Click(object sender, RoutedEventArgs e)
        {
            projectsDataGrid.SelectAll();
        }

        private void BtnSelectNone_Click(object sender, RoutedEventArgs e)
        {
            projectsDataGrid.UnselectAll();
        }

        private async void BtnImportCSV_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Excel files (*.xlsx)|*.xlsx|CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                Title = "Select file to import"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    statusText.Text = "Analyzing file...";

                    var fileExtension = Path.GetExtension(openFileDialog.FileName).ToLower();

                    if (fileExtension == ".xlsx")
                    {
                        MessageBox.Show("Excel import is temporarily disabled. Please convert to CSV format.", "Excel Import",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    else if (fileExtension == ".csv")
                    {
                        var dialog = new CsvImportDialog(_teams);
                        if (dialog.ShowDialog() == true && dialog.ImportSuccessful)
                        {
                            await SaveImportedProjects(dialog.ImportedProjects);
                        }
                    }
                    else
                    {
                        MessageBox.Show("Please select an Excel (.xlsx) or CSV (.csv) file.", "Invalid File Type",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing file: {ex.Message}", "Import Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    statusText.Text = "Import failed";
                }
            }
        }

        private async Task HandleExcelImport(string filePath)
        {
            // First, analyze the Excel file structure
            var analysis = await _excelService.AnalyzeExcelFileAsync(filePath);

            if (!analysis.IsSuccess)
            {
                MessageBox.Show($"Error analyzing Excel file: {analysis.ErrorMessage}", "Analysis Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Show analysis results to user
            var analysisMessage = $"Excel File Analysis:\n\n" +
                                $"Worksheet: {analysis.WorksheetName}\n" +
                                $"Rows: {analysis.TotalRows} (including header)\n" +
                                $"Columns: {analysis.TotalColumns}\n\n" +
                                $"Headers found:\n{string.Join("\n", analysis.Headers.Select((h, i) => $"{i + 1}. {h}"))}\n\n" +
                                $"Would you like to proceed with importing this data?";

            var result = MessageBox.Show(analysisMessage, "Excel File Analysis",
                MessageBoxButton.YesNo, MessageBoxImage.Information);

            if (result == MessageBoxResult.Yes)
            {
                // Convert Excel to CSV format and use existing CSV import dialog
                var tempCsvPath = Path.GetTempFileName() + ".csv";
                var conversionResult = await _excelService.ConvertExcelToCsvAsync(filePath, tempCsvPath);

                if (conversionResult == "Success")
                {
                    var dialog = new CsvImportDialog(_teams);
                    // TODO: Pre-load the converted CSV file in the dialog
                    if (dialog.ShowDialog() == true && dialog.ImportSuccessful)
                    {
                        await SaveImportedProjects(dialog.ImportedProjects);
                    }

                    // Clean up temp file
                    try { File.Delete(tempCsvPath); } catch { }
                }
                else
                {
                    MessageBox.Show($"Error converting Excel to CSV: {conversionResult}", "Conversion Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task SaveImportedProjects(List<Project> projects)
        {
            try
            {
                statusText.Text = "Saving imported projects...";

                for (int i = 0; i < projects.Count; i++)
                {
                    var project = projects[i];
                    try
                    {
                        // Validate project data before saving
                        ValidateProjectForSave(project);

                        statusText.Text = $"Saving project {i + 1} of {projects.Count}: {project.Name}";
                        await _databaseService.AddProjectAsync(project);
                    }
                    catch (Exception projectEx)
                    {
                        var errorMsg = $"Error saving project '{project.Name}' (row {i + 1}):\n" +
                                     $"Name: {project.Name}\n" +
                                     $"Customer: {project.CustomerName}\n" +
                                     $"Start Date: {project.StartDate}\n" +
                                     $"End Date: {project.EndDate}\n" +
                                     $"Team ID: {project.TeamId}\n" +
                                     $"Equipment Type: {project.EquipmentType}\n\n" +
                                     $"Error: {projectEx.Message}";

                        if (projectEx.InnerException != null)
                        {
                            errorMsg += $"\nInner Exception: {projectEx.InnerException.Message}";
                        }

                        MessageBox.Show(errorMsg, "Project Save Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);

                        statusText.Text = $"Failed to save project {i + 1}";
                        return; // Stop processing on first error
                    }
                }

                await InitializeDataAsync();
                UpdateStatusBar();
                statusText.Text = $"Successfully imported {projects.Count} projects";

                MessageBox.Show($"Successfully imported {projects.Count} projects!", "Import Complete",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                var detailedError = $"Error during import process:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    detailedError += $"\nInner Exception: {ex.InnerException.Message}";
                }

                MessageBox.Show(detailedError, "Import Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                statusText.Text = "Import failed";
            }
        }

        private void ValidateProjectForSave(Project project)
        {
            if (string.IsNullOrWhiteSpace(project.Name))
                throw new ArgumentException("Project name cannot be empty");

            if (project.Name.Length > 200)
                throw new ArgumentException($"Project name too long: {project.Name.Length} characters (max 200)");

            if (!string.IsNullOrEmpty(project.CustomerName) && project.CustomerName.Length > 200)
                throw new ArgumentException($"Customer name too long: {project.CustomerName.Length} characters (max 200)");

            if (!string.IsNullOrEmpty(project.ProjectRef) && project.ProjectRef.Length > 100)
                throw new ArgumentException($"Project reference too long: {project.ProjectRef.Length} characters (max 100)");

            if (project.StartDate > project.EndDate)
                throw new ArgumentException($"Start date ({project.StartDate:yyyy-MM-dd}) cannot be after end date ({project.EndDate:yyyy-MM-dd})");

            if (project.TeamId <= 0)
                throw new ArgumentException($"Invalid team ID: {project.TeamId}");

            // Check if team exists
            if (!_teams.Any(t => t.Id == project.TeamId))
                throw new ArgumentException($"Team with ID {project.TeamId} does not exist");

            if (string.IsNullOrWhiteSpace(project.EquipmentType))
                throw new ArgumentException("Equipment type cannot be empty");

            // Check if EquipmentType is valid
            if (!EquipmentType.All.Contains(project.EquipmentType))
                throw new ArgumentException($"Invalid equipment type: {project.EquipmentType}. Valid types: {string.Join(", ", EquipmentType.All)}");
        }

        private async void BtnExportCSV_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                Title = "Export projects to CSV",
                FileName = $"Projects_Export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    statusText.Text = "Exporting projects...";
                    await ExportProjectsToCsv(saveFileDialog.FileName);
                    statusText.Text = "Export completed successfully";
                    MessageBox.Show($"Projects exported to: {saveFileDialog.FileName}", "Export Complete",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting projects: {ex.Message}", "Export Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    statusText.Text = "Export failed";
                }
            }
        }

        private async Task ExportProjectsToCsv(string filePath)
        {
            await Task.Run(() =>
            {
                using var writer = new StreamWriter(filePath);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write headers
                csv.WriteField("ProjectRef");
                csv.WriteField("Project Name");
                csv.WriteField("Customer Name");
                csv.WriteField("Description");
                csv.WriteField("Team Name");
                csv.WriteField("Equipment Type");
                csv.WriteField("Start Date");
                csv.WriteField("End Date");
                csv.WriteField("Status");
                csv.WriteField("Estimated Hours");
                csv.WriteField("Notes");
                csv.NextRecord();

                // Write project data
                foreach (var project in _projects)
                {
                    csv.WriteField(project.ProjectRef ?? string.Empty);
                    csv.WriteField(project.Name);
                    csv.WriteField(project.CustomerName ?? string.Empty);
                    csv.WriteField(project.Description ?? string.Empty);
                    csv.WriteField(project.Team?.Name ?? string.Empty);
                    csv.WriteField(project.EquipmentType);
                    csv.WriteField(project.StartDate.ToString("yyyy-MM-dd"));
                    csv.WriteField(project.EndDate.ToString("yyyy-MM-dd"));
                    csv.WriteField(project.Status);
                    csv.WriteField(project.EstimatedHours?.ToString() ?? string.Empty);
                    csv.WriteField(project.Notes ?? string.Empty);
                    csv.NextRecord();
                }
            });
        }

        // Gantt Chart Event Handlers
        private void BtnApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedTeam = cmbTeamFilter.SelectedItem as Team;
                var selectedEquipmentType = cmbEquipmentFilter.SelectedItem as string;

                var filteredProjects = _projects.AsEnumerable();

                // Apply team filter
                if (selectedTeam != null && selectedTeam.Id > 0)
                {
                    filteredProjects = filteredProjects.Where(p => p.TeamId == selectedTeam.Id);
                }

                // Apply equipment type filter
                if (!string.IsNullOrEmpty(selectedEquipmentType) && selectedEquipmentType != "All Types")
                {
                    filteredProjects = filteredProjects.Where(p => p.EquipmentType == selectedEquipmentType);
                }

                ganttChart.LoadProjects(filteredProjects.ToList());
                statusText.Text = $"Gantt chart filtered: {filteredProjects.Count()} projects shown";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying filters: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
        {
            cmbTeamFilter.SelectedIndex = 0;
            cmbEquipmentFilter.SelectedIndex = 0;
            ganttChart.LoadProjects(_projects);
            statusText.Text = "Gantt chart filters cleared";
        }

        private async void GanttChart_ProjectClicked(object sender, Project project)
        {
            // Open edit project dialog when Gantt bar is clicked
            var dialog = new ProjectFormDialog(_teams, project);
            if (dialog.ShowDialog() == true && dialog.Project != null)
            {
                try
                {
                    await _databaseService.UpdateProjectAsync(dialog.Project);
                    await InitializeDataAsync();
                    UpdateStatusBar();
                    statusText.Text = "Project updated successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error updating project: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Reports Event Handlers
        private void BtnProjectSummary_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var summary = _projects.Select(p => new
                {
                    p.Id,
                    p.Name,
                    Team = p.Team?.Name ?? "Unassigned",
                    p.EquipmentType,
                    p.Status,
                    p.StartDate,
                    p.EndDate,
                    p.EstimatedHours,
                    TaskCount = p.Tasks?.Count ?? 0
                }).ToList();

                summaryDataGrid.ItemsSource = summary;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Project summary report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating project summary: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnTeamWorkload_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var workload = _teams.Select(t => new
                {
                    TeamName = t.Name,
                    ProjectCount = _projects.Count(p => p.TeamId == t.Id),
                    TotalEstimatedHours = _projects.Where(p => p.TeamId == t.Id).Sum(p => p.EstimatedHours ?? 0),
                    InProgressProjects = _projects.Count(p => p.TeamId == t.Id && p.Status == ProjectStatus.InProgress),
                    CompletedProjects = _projects.Count(p => p.TeamId == t.Id && p.Status == ProjectStatus.Completed)
                }).ToList();

                summaryDataGrid.ItemsSource = workload;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Team workload report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating team workload report: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEquipmentBreakdown_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var breakdown = _projects.GroupBy(p => p.EquipmentType).Select(g => new
                {
                    EquipmentType = g.Key,
                    ProjectCount = g.Count(),
                    TotalEstimatedHours = g.Sum(p => p.EstimatedHours ?? 0),
                    InProgressCount = g.Count(p => p.Status == ProjectStatus.InProgress),
                    CompletedCount = g.Count(p => p.Status == ProjectStatus.Completed),
                    NotStartedCount = g.Count(p => p.Status == ProjectStatus.NotStarted)
                }).ToList();

                summaryDataGrid.ItemsSource = breakdown;
                reportsTabControl.SelectedIndex = 0; // Summary tab
                statusText.Text = "Equipment breakdown report generated";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error generating equipment breakdown report: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement PDF export
            MessageBox.Show("PDF export functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement Excel export
            MessageBox.Show("Excel export functionality will be implemented in the next phase.",
                "Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Team Management Event Handlers
        private async void BtnAddTeam_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new TeamFormDialog();
            if (dialog.ShowDialog() == true && dialog.Team != null)
            {
                try
                {
                    await _databaseService.AddTeamAsync(dialog.Team);
                    await InitializeDataAsync();
                    SetupComboBoxes();
                    UpdateStatusBar();
                    statusText.Text = "Team added successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding team: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnEditTeam_Click(object sender, RoutedEventArgs e)
        {
            if (teamsDataGrid.SelectedItem is Team selectedTeam)
            {
                var dialog = new TeamFormDialog(selectedTeam);
                if (dialog.ShowDialog() == true && dialog.Team != null)
                {
                    try
                    {
                        await _databaseService.UpdateTeamAsync(dialog.Team);
                        await InitializeDataAsync();
                        SetupComboBoxes();
                        UpdateStatusBar();
                        statusText.Text = "Team updated successfully";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error updating team: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a team to edit.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void BtnDeleteTeam_Click(object sender, RoutedEventArgs e)
        {
            if (teamsDataGrid.SelectedItem is Team selectedTeam)
            {
                // Check if team has projects
                var teamProjects = _projects.Where(p => p.TeamId == selectedTeam.Id).ToList();
                if (teamProjects.Any())
                {
                    MessageBox.Show($"Cannot delete team '{selectedTeam.Name}' because it has {teamProjects.Count} assigned project(s).",
                        "Cannot Delete", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"Are you sure you want to delete team '{selectedTeam.Name}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _databaseService.DeleteTeamAsync(selectedTeam.Id);
                        await InitializeDataAsync();
                        SetupComboBoxes();
                        UpdateStatusBar();
                        statusText.Text = "Team deleted successfully";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting team: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("Please select a team to delete.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void BtnSaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Save theme setting
                var isDarkMode = chkDarkMode.IsChecked ?? false;
                await _settingsService.UpdateSettingAsync("darkmode", isDarkMode);
                ThemeManager.Instance.SetTheme(isDarkMode);

                // Save other settings
                await _settingsService.UpdateSettingAsync("autosave", chkAutoSave.IsChecked ?? true);
                await _settingsService.UpdateSettingAsync("shownotifications", chkShowNotifications.IsChecked ?? true);

                // Save default values
                await _settingsService.UpdateSettingAsync("defaultequipmenttype", cmbDefaultEquipmentType.SelectedItem?.ToString() ?? "ISP");
                await _settingsService.UpdateSettingAsync("defaultprojectstatus", cmbDefaultProjectStatus.SelectedItem?.ToString() ?? "Not Started");

                statusText.Text = "Settings saved successfully";
                MessageBox.Show("Settings have been saved successfully!", "Settings Saved",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MenuAbout_Click(object sender, RoutedEventArgs e)
        {
            var aboutDialog = new AboutDialog();
            aboutDialog.Owner = this;
            aboutDialog.ShowDialog();
        }
    }
}
