﻿#pragma checksum "..\..\..\..\..\Controls\GanttChart.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "34B4125F26C92CBF8F5C57EA5E8DCA4065C1CF49"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ProjectManager.WPF.Controls {
    
    
    /// <summary>
    /// GanttChart
    /// </summary>
    public partial class GanttChart : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbTimeScale;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnZoomIn;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnZoomOut;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFitToWindow;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGoToToday;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer projectListScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel projectListPanel;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer timelineHeaderScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas timelineHeaderCanvas;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer timelineScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Controls\GanttChart.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas timelineCanvas;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ProjectManager.WPF;component/controls/ganttchart.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Controls\GanttChart.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.cmbTimeScale = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.btnZoomIn = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\..\Controls\GanttChart.xaml"
            this.btnZoomIn.Click += new System.Windows.RoutedEventHandler(this.BtnZoomIn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnZoomOut = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\Controls\GanttChart.xaml"
            this.btnZoomOut.Click += new System.Windows.RoutedEventHandler(this.BtnZoomOut_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnFitToWindow = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\..\Controls\GanttChart.xaml"
            this.btnFitToWindow.Click += new System.Windows.RoutedEventHandler(this.BtnFitToWindow_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnGoToToday = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Controls\GanttChart.xaml"
            this.btnGoToToday.Click += new System.Windows.RoutedEventHandler(this.BtnGoToToday_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.projectListScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 7:
            this.projectListPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.timelineHeaderScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 9:
            this.timelineHeaderCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 10:
            this.timelineScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            
            #line 129 "..\..\..\..\..\Controls\GanttChart.xaml"
            this.timelineScrollViewer.ScrollChanged += new System.Windows.Controls.ScrollChangedEventHandler(this.TimelineScrollViewer_ScrollChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.timelineCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

