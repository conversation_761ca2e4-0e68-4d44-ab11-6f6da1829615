@echo off
echo.
echo ========================================
echo   Octopi Project Manager v1.1 Installer
echo   Developed by <PERSON> (AntmanZA)
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Installing Octopi Project Manager v1.1...
    echo.
    
    REM Create installation directory
    set INSTALL_DIR=%ProgramFiles%\OctopiProjectManager
    if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
    
    echo Copying application files...
    copy /Y "ProjectManager.WPF.exe" "%INSTALL_DIR%\"
    copy /Y "ProjectManager.WPF.pdb" "%INSTALL_DIR%\"
    copy /Y "ProjectManager.WPF.runtimeconfig.json" "%INSTALL_DIR%\"
    copy /Y "ProjectManager.WPF.deps.json" "%INSTALL_DIR%\"
    copy /Y "*.dll" "%INSTALL_DIR%\"
    
    echo Creating shortcuts...
    
    REM Create desktop shortcut
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Octopi Project Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\ProjectManager.WPF.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Octopi Project Manager - Project Tracker'; $Shortcut.Save()"
    
    REM Create start menu shortcut
    if not exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager" mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager"
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager\Octopi Project Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\ProjectManager.WPF.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Octopi Project Manager - Project Tracker'; $Shortcut.Save()"
    
    REM Register in Add/Remove Programs
    echo Registering application...
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "DisplayName" /t REG_SZ /d "Octopi Project Manager" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "DisplayVersion" /t REG_SZ /d "1.1.0" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "Publisher" /t REG_SZ /d "Conrad Cloete (AntmanZA)" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\ProjectManager.WPF.exe" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\UNINSTALL.bat" /f
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /v "EstimatedSize" /t REG_DWORD /d 160000 /f
    
    REM Create uninstaller
    echo Creating uninstaller...
    (
    echo @echo off
    echo echo Uninstalling Octopi Project Manager...
    echo.
    echo REM Remove shortcuts
    echo del "%USERPROFILE%\Desktop\Octopi Project Manager.lnk" 2^>nul
    echo rmdir /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager" 2^>nul
    echo.
    echo REM Remove registry entry
    echo reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /f 2^>nul
    echo.
    echo REM Remove application files
    echo cd /d "%ProgramFiles%"
    echo rmdir /s /q "OctopiProjectManager" 2^>nul
    echo.
    echo echo Octopi Project Manager has been uninstalled successfully!
    echo pause
    ) > "%INSTALL_DIR%\UNINSTALL.bat"
    
    echo.
    echo ========================================
    echo Installation completed successfully!
    echo.
    echo You can now run Octopi Project Manager from:
    echo - Desktop shortcut
    echo - Start Menu
    echo - %INSTALL_DIR%\ProjectManager.WPF.exe
    echo ========================================
    echo.
    pause
    
) else (
    echo This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
)
