# Create Executable Installer Package
Write-Host "Creating Octopi Project Manager Executable Installer..." -ForegroundColor Green

# Check if ps2exe is available
try {
    Get-Command ps2exe -ErrorAction Stop | Out-Null
    Write-Host "ps2exe found, creating executable installer..." -ForegroundColor Yellow
    
    # Create executable installer
    ps2exe -inputFile "OctopiProjectManagerInstaller.ps1" -outputFile "OctopiProjectManagerSetup.exe" -iconFile "..\ProjectManager.WPF\octopi.ico" -title "Octopi Project Manager Installer" -description "Octopi Project Manager Professional Installer" -company "Conrad Cloete (AntmanZA)" -version "*******" -copyright "Copyright (c) 2025 AntmanZa" -requireAdmin
    
    if (Test-Path "OctopiProjectManagerSetup.exe") {
        Write-Host "✅ Executable installer created: OctopiProjectManagerSetup.exe" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create executable installer" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ps2exe not found. Installing ps2exe module..." -ForegroundColor Yellow
    
    try {
        Install-Module -Name ps2exe -Force -Scope CurrentUser
        Write-Host "ps2exe installed successfully. Creating executable..." -ForegroundColor Green
        
        ps2exe -inputFile "OctopiProjectManagerInstaller.ps1" -outputFile "OctopiProjectManagerSetup.exe" -iconFile "..\ProjectManager.WPF\octopi.ico" -title "Octopi Project Manager Installer" -description "Octopi Project Manager Professional Installer" -company "Conrad Cloete (AntmanZA)" -version "*******" -copyright "Copyright (c) 2025 AntmanZa" -requireAdmin
        
        if (Test-Path "OctopiProjectManagerSetup.exe") {
            Write-Host "✅ Executable installer created: OctopiProjectManagerSetup.exe" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "⚠️  Could not install ps2exe. Using PowerShell script installer instead." -ForegroundColor Yellow
        Write-Host "You can manually install ps2exe with: Install-Module ps2exe" -ForegroundColor Cyan
    }
}

# Create installer package
Write-Host "`nCreating complete installer package..." -ForegroundColor Green

$packagePath = "..\OctopiProjectManager_Professional_Installer"
if (Test-Path $packagePath) {
    Remove-Item $packagePath -Recurse -Force
}
New-Item -ItemType Directory -Path $packagePath -Force | Out-Null

# Copy installer files
Copy-Item "OctopiProjectManagerInstaller.ps1" -Destination $packagePath
Copy-Item "RunInstaller.bat" -Destination $packagePath
Copy-Item "License.rtf" -Destination $packagePath

# Copy executable installer if it exists
if (Test-Path "OctopiProjectManagerSetup.exe") {
    Copy-Item "OctopiProjectManagerSetup.exe" -Destination $packagePath
}

# Copy application files
Copy-Item "..\OctopiProjectManager_Deployment\*" -Destination $packagePath -Recurse

# Create README for installer package
$readmeContent = @"
========================================
    OCTOPI PROJECT MANAGER v1.0
    PROFESSIONAL INSTALLER PACKAGE
    Developed by Conrad Cloete (AntmanZA)
========================================

INSTALLATION OPTIONS:
--------------------

Option 1: GUI Installer (Recommended)
- Right-click "RunInstaller.bat" and select "Run as administrator"
- Follow the graphical installation wizard
- Choose installation path and options

Option 2: Executable Installer (If available)
- Right-click "OctopiProjectManagerSetup.exe" and select "Run as administrator"
- Professional Windows installer with GUI

Option 3: PowerShell Installer
- Open PowerShell as administrator
- Run: .\OctopiProjectManagerInstaller.ps1

Option 4: Manual Installation
- Copy ProjectManager.WPF.exe to desired location
- Run directly (portable mode)

FEATURES:
---------
• Professional GUI installer
• Automatic shortcut creation
• Add/Remove Programs integration
• Clean uninstaller
• Administrator privilege handling
• Custom installation path selection

SYSTEM REQUIREMENTS:
-------------------
• Windows 10 or later (64-bit)
• Administrator privileges for installation
• No additional software required

UNINSTALLATION:
--------------
• Use Add/Remove Programs in Windows Settings
• Or run: PowerShell -ExecutionPolicy Bypass -File "OctopiProjectManagerInstaller.ps1" -Uninstall

========================================
© 2025 Conrad Cloete (AntmanZA)
========================================
"@

Set-Content -Path "$packagePath\INSTALLER_README.txt" -Value $readmeContent

Write-Host "✅ Professional installer package created in: $packagePath" -ForegroundColor Green

# Show package contents
Write-Host "`nPackage contents:" -ForegroundColor Cyan
Get-ChildItem $packagePath | ForEach-Object {
    $size = if ($_.PSIsContainer) { "Folder" } else { "{0:N2} MB" -f ($_.Length / 1MB) }
    Write-Host "  📄 $($_.Name) - $size" -ForegroundColor White
}

Write-Host "`n🎉 Professional installer package ready for distribution!" -ForegroundColor Green
