﻿#pragma checksum "..\..\..\..\Views\DepartmentManagementDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FB36D1DD396168130FBAA699EB2BC3B7E1CA8F15"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ProjectManager.WPF.Views {
    
    
    /// <summary>
    /// DepartmentManagementDialog
    /// </summary>
    public partial class DepartmentManagementDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 50 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddDepartment;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditDepartment;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteDepartment;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid departmentDataGrid;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusText;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ProjectManager.WPF;component/views/departmentmanagementdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnAddDepartment = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.btnAddDepartment.Click += new System.Windows.RoutedEventHandler(this.BtnAddDepartment_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnEditDepartment = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.btnEditDepartment.Click += new System.Windows.RoutedEventHandler(this.BtnEditDepartment_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnDeleteDepartment = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.btnDeleteDepartment.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteDepartment_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.departmentDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 75 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.departmentDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DepartmentDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.statusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Views\DepartmentManagementDialog.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

