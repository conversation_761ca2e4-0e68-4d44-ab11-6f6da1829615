using System;
using System.Drawing;
using System.Windows.Forms;

namespace OctopiProjectManagerInstaller
{
    public class UninstallProgressForm : Form
    {
        private ProgressBar progressBar;
        private Label statusLabel;
        private Label headerLabel;

        public UninstallProgressForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Uninstalling Octopi Project Manager";
            this.Size = new Size(450, 200);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ControlBox = false; // Prevent closing during uninstall

            // Header
            headerLabel = new Label
            {
                Text = "🐙 Uninstalling Octopi Project Manager",
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                Location = new Point(20, 20),
                Size = new Size(400, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(headerLabel);

            // Status label
            statusLabel = new Label
            {
                Text = "Preparing to uninstall...",
                Font = new Font("Arial", 9),
                Location = new Point(20, 60),
                Size = new Size(400, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(statusLabel);

            // Progress bar
            progressBar = new ProgressBar
            {
                Location = new Point(20, 90),
                Size = new Size(400, 25),
                Style = ProgressBarStyle.Continuous,
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            this.Controls.Add(progressBar);

            // Info label
            var infoLabel = new Label
            {
                Text = "Please wait while Octopi Project Manager is being removed...",
                Font = new Font("Arial", 8),
                ForeColor = Color.Gray,
                Location = new Point(20, 130),
                Size = new Size(400, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(infoLabel);
        }

        public void UpdateProgress(string status, int percentage)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, int>(UpdateProgress), status, percentage);
                return;
            }

            statusLabel.Text = status;
            progressBar.Value = Math.Min(percentage, 100);
            Application.DoEvents();
            
            // Small delay to show progress
            System.Threading.Thread.Sleep(200);
        }

        protected override void SetVisibleCore(bool value)
        {
            base.SetVisibleCore(value);
            if (value)
            {
                this.BringToFront();
                this.Focus();
            }
        }
    }
}
