<Window x:Class="ProjectManager.WPF.Views.ProjectFormDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Project Details" 
        Height="600" Width="500" 
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Red"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="0,-10,0,10"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Name="headerText"
                   Text="Add New Project" 
                   FontSize="20" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Project Reference -->
                <TextBlock Text="Project Reference" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtProjectRef" Style="{StaticResource InputStyle}" MaxLength="100"/>

                <!-- Project Name -->
                <TextBlock Text="Project Name *" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtProjectName" Style="{StaticResource InputStyle}" MaxLength="200"/>
                <TextBlock Name="errorProjectName" Text="Project name is required" Style="{StaticResource ErrorTextStyle}"/>

                <!-- Customer Name -->
                <TextBlock Text="Customer Name" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtCustomerName" Style="{StaticResource InputStyle}" MaxLength="200"/>

                <!-- Description -->
                <TextBlock Text="Description" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtDescription" 
                         Style="{StaticResource InputStyle}" 
                         Height="80" 
                         TextWrapping="Wrap" 
                         AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto"
                         MaxLength="1000"/>

                <!-- Team Assignment -->
                <TextBlock Text="Assigned Team *" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="cmbTeam" 
                          Style="{StaticResource InputStyle}"
                          DisplayMemberPath="Name"
                          SelectedValuePath="Id"/>
                <TextBlock Name="errorTeam" Text="Please select a team" Style="{StaticResource ErrorTextStyle}"/>

                <!-- Equipment Type -->
                <TextBlock Text="Equipment Type *" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="cmbEquipmentType" Style="{StaticResource InputStyle}"/>
                <TextBlock Name="errorEquipmentType" Text="Please select equipment type" Style="{StaticResource ErrorTextStyle}"/>

                <!-- Date Range -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="Start Date *" Style="{StaticResource LabelStyle}"/>
                        <DatePicker Name="dpStartDate" Style="{StaticResource InputStyle}"/>
                        <TextBlock Name="errorStartDate" Text="Start date is required" Style="{StaticResource ErrorTextStyle}"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="End Date *" Style="{StaticResource LabelStyle}"/>
                        <DatePicker Name="dpEndDate" Style="{StaticResource InputStyle}"/>
                        <TextBlock Name="errorEndDate" Text="End date is required and must be after start date" Style="{StaticResource ErrorTextStyle}"/>
                    </StackPanel>
                </Grid>

                <!-- Status -->
                <TextBlock Text="Status" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="cmbStatus" Style="{StaticResource InputStyle}"/>

                <!-- Estimated Hours -->
                <TextBlock Text="Estimated Hours" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtEstimatedHours" Style="{StaticResource InputStyle}" MaxLength="10"/>
                <TextBlock Name="errorEstimatedHours" Text="Please enter a valid number" Style="{StaticResource ErrorTextStyle}"/>

                <!-- Notes -->
                <TextBlock Text="Notes" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="txtNotes" 
                         Style="{StaticResource InputStyle}" 
                         Height="100" 
                         TextWrapping="Wrap" 
                         AcceptsReturn="True" 
                         VerticalScrollBarVisibility="Auto"
                         MaxLength="2000"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Name="btnSave" 
                    Content="Save" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#A3BE8C" 
                    Foreground="White" 
                    BorderBrush="Transparent"
                    Click="BtnSave_Click"/>
            <Button Name="btnCancel" 
                    Content="Cancel" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#4C566A" 
                    Foreground="White" 
                    BorderBrush="Transparent"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
