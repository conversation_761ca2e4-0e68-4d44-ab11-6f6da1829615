{"version": 2, "dgSpecHash": "zwITIMOo1UHu9NREjnT2McCRbgYwY2INqC1506ONPWcg7UtHA2wBOJ2qYNu0EEMB2rqHMuA5jCDvwK9NPipU9A==", "success": true, "projectFilePath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Projectmanager\\OctopiInstaller\\StandaloneInstaller\\OctopiStandaloneInstaller.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.win-x64\\6.0.36\\microsoft.netcore.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.runtime.win-x64\\6.0.36\\microsoft.windowsdesktop.app.runtime.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.win-x64\\6.0.36\\microsoft.aspnetcore.app.runtime.win-x64.6.0.36.nupkg.sha512"], "logs": []}