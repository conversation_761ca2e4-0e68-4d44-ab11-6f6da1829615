# Octopi Project Manager Professional Installer
# Developed by <PERSON> (AntmanZA)
# Copyright (c) 2025 Antman<PERSON>a

param(
    [switch]$Uninstall,
    [switch]$Silent
)

# Configuration
$AppName = "Octopi Project Manager"
$AppVersion = "1.0.0"
$Publisher = "<PERSON> (AntmanZA)"
$InstallPath = "$env:ProgramFiles\OctopiProjectManager"
$AppExe = "ProjectManager.WPF.exe"
$AppDescription = "Comprehensive project management solution designed for Octopi Smart Solutions"

# Check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Create GUI installer
function Show-InstallerGUI {
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing

    # Create form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "$AppName Installer"
    $form.Size = New-Object System.Drawing.Size(500, 400)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false

    # Header
    $headerLabel = New-Object System.Windows.Forms.Label
    $headerLabel.Text = "🐙 $AppName"
    $headerLabel.Font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
    $headerLabel.ForeColor = [System.Drawing.Color]::DarkBlue
    $headerLabel.Location = New-Object System.Drawing.Point(20, 20)
    $headerLabel.Size = New-Object System.Drawing.Size(450, 30)
    $form.Controls.Add($headerLabel)

    # Version
    $versionLabel = New-Object System.Windows.Forms.Label
    $versionLabel.Text = "Version $AppVersion"
    $versionLabel.Font = New-Object System.Drawing.Font("Arial", 10)
    $versionLabel.Location = New-Object System.Drawing.Point(20, 50)
    $versionLabel.Size = New-Object System.Drawing.Size(200, 20)
    $form.Controls.Add($versionLabel)

    # Description
    $descLabel = New-Object System.Windows.Forms.Label
    $descLabel.Text = $AppDescription
    $descLabel.Font = New-Object System.Drawing.Font("Arial", 9)
    $descLabel.Location = New-Object System.Drawing.Point(20, 80)
    $descLabel.Size = New-Object System.Drawing.Size(450, 40)
    $form.Controls.Add($descLabel)

    # Developer
    $devLabel = New-Object System.Windows.Forms.Label
    $devLabel.Text = "Developed by $Publisher"
    $devLabel.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Italic)
    $devLabel.Location = New-Object System.Drawing.Point(20, 120)
    $devLabel.Size = New-Object System.Drawing.Size(300, 20)
    $form.Controls.Add($devLabel)

    # Install path
    $pathLabel = New-Object System.Windows.Forms.Label
    $pathLabel.Text = "Installation Path:"
    $pathLabel.Location = New-Object System.Drawing.Point(20, 160)
    $pathLabel.Size = New-Object System.Drawing.Size(100, 20)
    $form.Controls.Add($pathLabel)

    $pathTextBox = New-Object System.Windows.Forms.TextBox
    $pathTextBox.Text = $InstallPath
    $pathTextBox.Location = New-Object System.Drawing.Point(20, 180)
    $pathTextBox.Size = New-Object System.Drawing.Size(350, 20)
    $form.Controls.Add($pathTextBox)

    $browseButton = New-Object System.Windows.Forms.Button
    $browseButton.Text = "Browse..."
    $browseButton.Location = New-Object System.Drawing.Point(380, 178)
    $browseButton.Size = New-Object System.Drawing.Size(80, 25)
    $browseButton.Add_Click({
        $folderDialog = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderDialog.Description = "Select installation folder"
        $folderDialog.SelectedPath = $pathTextBox.Text
        if ($folderDialog.ShowDialog() -eq "OK") {
            $pathTextBox.Text = $folderDialog.SelectedPath + "\OctopiProjectManager"
        }
    })
    $form.Controls.Add($browseButton)

    # Options
    $desktopCheckBox = New-Object System.Windows.Forms.CheckBox
    $desktopCheckBox.Text = "Create desktop shortcut"
    $desktopCheckBox.Checked = $true
    $desktopCheckBox.Location = New-Object System.Drawing.Point(20, 220)
    $desktopCheckBox.Size = New-Object System.Drawing.Size(200, 20)
    $form.Controls.Add($desktopCheckBox)

    $startMenuCheckBox = New-Object System.Windows.Forms.CheckBox
    $startMenuCheckBox.Text = "Create Start Menu shortcuts"
    $startMenuCheckBox.Checked = $true
    $startMenuCheckBox.Location = New-Object System.Drawing.Point(20, 245)
    $startMenuCheckBox.Size = New-Object System.Drawing.Size(200, 20)
    $form.Controls.Add($startMenuCheckBox)

    # Progress bar
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 280)
    $progressBar.Size = New-Object System.Drawing.Size(450, 20)
    $progressBar.Visible = $false
    $form.Controls.Add($progressBar)

    # Status label
    $statusLabel = New-Object System.Windows.Forms.Label
    $statusLabel.Text = "Ready to install"
    $statusLabel.Location = New-Object System.Drawing.Point(20, 305)
    $statusLabel.Size = New-Object System.Drawing.Size(300, 20)
    $form.Controls.Add($statusLabel)

    # Buttons
    $installButton = New-Object System.Windows.Forms.Button
    $installButton.Text = "Install"
    $installButton.Location = New-Object System.Drawing.Point(300, 330)
    $installButton.Size = New-Object System.Drawing.Size(80, 30)
    $installButton.BackColor = [System.Drawing.Color]::LightGreen
    $form.Controls.Add($installButton)

    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Text = "Cancel"
    $cancelButton.Location = New-Object System.Drawing.Point(390, 330)
    $cancelButton.Size = New-Object System.Drawing.Size(80, 30)
    $cancelButton.Add_Click({ $form.Close() })
    $form.Controls.Add($cancelButton)

    # Install button click event
    $installButton.Add_Click({
        $installButton.Enabled = $false
        $cancelButton.Enabled = $false
        $progressBar.Visible = $true
        
        $targetPath = $pathTextBox.Text
        $createDesktop = $desktopCheckBox.Checked
        $createStartMenu = $startMenuCheckBox.Checked
        
        Install-Application -InstallPath $targetPath -CreateDesktop $createDesktop -CreateStartMenu $createStartMenu -StatusLabel $statusLabel -ProgressBar $progressBar -Form $form
    })

    # Show form
    $form.ShowDialog()
}

# Installation function
function Install-Application {
    param(
        $InstallPath,
        $CreateDesktop,
        $CreateStartMenu,
        $StatusLabel,
        $ProgressBar,
        $Form
    )
    
    try {
        # Update status
        $StatusLabel.Text = "Creating installation directory..."
        $ProgressBar.Value = 10
        $Form.Refresh()
        Start-Sleep -Milliseconds 500

        # Create installation directory
        if (!(Test-Path $InstallPath)) {
            New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
        }

        # Copy files
        $StatusLabel.Text = "Copying application files..."
        $ProgressBar.Value = 30
        $Form.Refresh()
        
        $sourceFiles = @(
            "..\OctopiProjectManager_Deployment\ProjectManager.WPF.exe",
            "..\OctopiProjectManager_Deployment\ProjectManager.WPF.pdb"
        )
        
        foreach ($file in $sourceFiles) {
            if (Test-Path $file) {
                Copy-Item $file -Destination $InstallPath -Force
            }
        }

        # Create shortcuts
        if ($CreateDesktop) {
            $StatusLabel.Text = "Creating desktop shortcut..."
            $ProgressBar.Value = 60
            $Form.Refresh()
            
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\$AppName.lnk")
            $Shortcut.TargetPath = "$InstallPath\$AppExe"
            $Shortcut.WorkingDirectory = $InstallPath
            $Shortcut.Description = $AppDescription
            $Shortcut.Save()
        }

        if ($CreateStartMenu) {
            $StatusLabel.Text = "Creating Start Menu shortcuts..."
            $ProgressBar.Value = 80
            $Form.Refresh()
            
            $startMenuPath = "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\$AppName"
            if (!(Test-Path $startMenuPath)) {
                New-Item -ItemType Directory -Path $startMenuPath -Force | Out-Null
            }
            
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("$startMenuPath\$AppName.lnk")
            $Shortcut.TargetPath = "$InstallPath\$AppExe"
            $Shortcut.WorkingDirectory = $InstallPath
            $Shortcut.Description = $AppDescription
            $Shortcut.Save()
        }

        # Register in Add/Remove Programs
        $StatusLabel.Text = "Registering application..."
        $ProgressBar.Value = 90
        $Form.Refresh()
        
        $regPath = "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\$AppName"
        New-Item -Path $regPath -Force | Out-Null
        Set-ItemProperty -Path $regPath -Name "DisplayName" -Value $AppName
        Set-ItemProperty -Path $regPath -Name "DisplayVersion" -Value $AppVersion
        Set-ItemProperty -Path $regPath -Name "Publisher" -Value $Publisher
        Set-ItemProperty -Path $regPath -Name "InstallLocation" -Value $InstallPath
        Set-ItemProperty -Path $regPath -Name "DisplayIcon" -Value "$InstallPath\$AppExe"
        Set-ItemProperty -Path $regPath -Name "UninstallString" -Value "powershell.exe -ExecutionPolicy Bypass -File `"$PSCommandPath`" -Uninstall"
        Set-ItemProperty -Path $regPath -Name "EstimatedSize" -Value 160000

        # Complete
        $StatusLabel.Text = "Installation completed successfully!"
        $ProgressBar.Value = 100
        $Form.Refresh()
        Start-Sleep -Seconds 2
        
        [System.Windows.Forms.MessageBox]::Show("$AppName has been installed successfully!`n`nInstallation Path: $InstallPath", "Installation Complete", "OK", "Information")
        $Form.Close()
        
    } catch {
        [System.Windows.Forms.MessageBox]::Show("Installation failed: $($_.Exception.Message)", "Installation Error", "OK", "Error")
        $Form.Close()
    }
}

# Uninstall function
function Uninstall-Application {
    if (!$Silent) {
        $result = [System.Windows.Forms.MessageBox]::Show("Are you sure you want to uninstall $AppName?", "Uninstall Confirmation", "YesNo", "Question")
        if ($result -eq "No") { return }
    }

    try {
        # Remove shortcuts
        Remove-Item "$env:USERPROFILE\Desktop\$AppName.lnk" -ErrorAction SilentlyContinue
        Remove-Item "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\$AppName" -Recurse -ErrorAction SilentlyContinue

        # Remove application files
        if (Test-Path $InstallPath) {
            Remove-Item $InstallPath -Recurse -Force
        }

        # Remove registry entry
        Remove-Item "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\$AppName" -ErrorAction SilentlyContinue

        if (!$Silent) {
            [System.Windows.Forms.MessageBox]::Show("$AppName has been uninstalled successfully!", "Uninstall Complete", "OK", "Information")
        }
    } catch {
        if (!$Silent) {
            [System.Windows.Forms.MessageBox]::Show("Uninstall failed: $($_.Exception.Message)", "Uninstall Error", "OK", "Error")
        }
    }
}

# Main execution
if (!$Silent) {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "   $AppName Installer v$AppVersion" -ForegroundColor Green
    Write-Host "   Developed by $Publisher" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

if (!(Test-Administrator)) {
    if (!$Silent) {
        [System.Windows.Forms.MessageBox]::Show("This installer requires administrator privileges. Please run as administrator.", "Administrator Required", "OK", "Warning")
    }
    exit 1
}

if ($Uninstall) {
    Uninstall-Application
} else {
    if ($Silent) {
        Install-Application -InstallPath $InstallPath -CreateDesktop $true -CreateStartMenu $true
    } else {
        Show-InstallerGUI
    }
}
