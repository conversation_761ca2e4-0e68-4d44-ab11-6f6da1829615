using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Services
{
    public class DepartmentService
    {
        private readonly ProjectManagerDbContext _context;
        private List<DepartmentEntity> _cachedDepartments = new();
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        public DepartmentService(ProjectManagerDbContext context)
        {
            _context = context;
        }

        public async Task<List<DepartmentEntity>> GetActiveDepartmentsAsync(bool forceRefresh = false)
        {
            if (forceRefresh || ShouldRefreshCache())
            {
                await RefreshCacheAsync();
            }

            return _cachedDepartments.Where(d => d.IsActive).ToList();
        }

        public async Task<List<DepartmentEntity>> GetAllDepartmentsAsync(bool forceRefresh = false)
        {
            if (forceRefresh || ShouldRefreshCache())
            {
                await RefreshCacheAsync();
            }

            return _cachedDepartments.ToList();
        }

        public async Task<DepartmentEntity?> GetDepartmentByIdAsync(int id)
        {
            await RefreshCacheIfNeeded();
            return _cachedDepartments.FirstOrDefault(d => d.Id == id);
        }

        public async Task<DepartmentEntity?> GetDepartmentByNameAsync(string name)
        {
            await RefreshCacheIfNeeded();
            return _cachedDepartments.FirstOrDefault(d =>
                d.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<List<string>> GetDepartmentNamesAsync(bool activeOnly = true)
        {
            var departments = await GetActiveDepartmentsAsync();
            if (!activeOnly)
            {
                departments = await GetAllDepartmentsAsync();
            }

            return departments.Select(d => d.Name).OrderBy(n => n).ToList();
        }

        public async Task<DepartmentEntity> CreateDepartmentAsync(DepartmentEntity department)
        {
            // Check for duplicate names
            var existing = await _context.Departments
                .FirstOrDefaultAsync(d => d.Name.ToLower() == department.Name.ToLower());

            if (existing != null)
            {
                throw new InvalidOperationException($"A department with the name '{department.Name}' already exists.");
            }

            department.CreatedDate = DateTime.Now;
            _context.Departments.Add(department);
            await _context.SaveChangesAsync();

            // Refresh cache
            await RefreshCacheAsync();

            return department;
        }

        public async Task<DepartmentEntity> UpdateDepartmentAsync(DepartmentEntity department)
        {
            // Check for duplicate names (excluding current department)
            var existing = await _context.Departments
                .FirstOrDefaultAsync(d => d.Name.ToLower() == department.Name.ToLower() && d.Id != department.Id);

            if (existing != null)
            {
                throw new InvalidOperationException($"A department with the name '{department.Name}' already exists.");
            }

            _context.Departments.Update(department);
            await _context.SaveChangesAsync();

            // Refresh cache
            await RefreshCacheAsync();

            return department;
        }

        public async Task DeleteDepartmentAsync(int departmentId)
        {
            var department = await _context.Departments
                .Include(d => d.Projects)
                .FirstOrDefaultAsync(d => d.Id == departmentId);

            if (department == null)
            {
                throw new InvalidOperationException("Department not found.");
            }

            // Remove department assignments from projects
            foreach (var project in department.Projects)
            {
                project.DepartmentId = null;
                project.EquipmentType = "Other"; // Fallback to "Other"
            }

            _context.Departments.Remove(department);
            await _context.SaveChangesAsync();

            // Refresh cache
            await RefreshCacheAsync();
        }

        public async Task<bool> DepartmentExistsAsync(string name, int? excludeId = null)
        {
            await RefreshCacheIfNeeded();
            
            return _cachedDepartments.Any(d => 
                d.Name.Equals(name, StringComparison.OrdinalIgnoreCase) && 
                (excludeId == null || d.Id != excludeId));
        }

        public async Task<int> GetProjectCountAsync(int departmentId)
        {
            return await _context.Projects.CountAsync(p => p.DepartmentId == departmentId);
        }

        public string GetDepartmentColor(string departmentName)
        {
            var department = _cachedDepartments.FirstOrDefault(d => 
                d.Name.Equals(departmentName, StringComparison.OrdinalIgnoreCase));
            
            return department?.Color ?? "#5E81AC"; // Default color
        }

        public async Task MigrateLegacyProjectsAsync()
        {
            // Migrate projects that only have EquipmentType but no DepartmentId
            var projectsToMigrate = await _context.Projects
                .Where(p => p.DepartmentId == null && !string.IsNullOrEmpty(p.EquipmentType))
                .ToListAsync();

            foreach (var project in projectsToMigrate)
            {
                var department = await GetDepartmentByNameAsync(project.EquipmentType);
                if (department != null)
                {
                    project.DepartmentId = department.Id;
                }
            }

            if (projectsToMigrate.Any())
            {
                await _context.SaveChangesAsync();
            }
        }

        private bool ShouldRefreshCache()
        {
            return _cachedDepartments.Count == 0 || 
                   DateTime.Now - _lastCacheUpdate > _cacheExpiry;
        }

        private async Task RefreshCacheIfNeeded()
        {
            if (ShouldRefreshCache())
            {
                await RefreshCacheAsync();
            }
        }

        private async Task RefreshCacheAsync()
        {
            _cachedDepartments = await _context.Departments
                .OrderBy(d => d.Name)
                .ToListAsync();
            
            _lastCacheUpdate = DateTime.Now;
        }

        public void ClearCache()
        {
            _cachedDepartments.Clear();
            _lastCacheUpdate = DateTime.MinValue;
        }
    }
}
