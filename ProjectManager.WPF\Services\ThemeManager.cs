using System;
using System.Windows;
using System.Windows.Media;

namespace ProjectManager.WPF.Services
{
    public class ThemeManager
    {
        private static ThemeManager? _instance;
        public static ThemeManager Instance => _instance ??= new ThemeManager();

        public event EventHandler<bool>? ThemeChanged;

        private bool _isDarkMode = false;
        public bool IsDarkMode 
        { 
            get => _isDarkMode;
            private set
            {
                if (_isDarkMode != value)
                {
                    _isDarkMode = value;
                    ThemeChanged?.Invoke(this, value);
                }
            }
        }

        // Octopi-inspired color palette
        public static class OctopiColors
        {
            // Primary Octopi Colors (inspired by ocean/sea themes)
            public static readonly Color OctopiPrimary = Color.FromRgb(0x1B, 0x4D, 0x6B); // Deep Ocean Blue
            public static readonly Color OctopiSecondary = Color.FromRgb(0x2E, 0x7D, 0x9A); // Ocean Blue
            public static readonly Color OctopiAccent = Color.FromRgb(0x4A, 0xB3, 0xD1); // Light Ocean Blue
            public static readonly Color OctopiTentacle = Color.FromRgb(0x6B, 0x4D, 0x8A); // Purple Tentacle
            public static readonly Color OctopiCoral = Color.FromRgb(0xE8, 0x7A, 0x5C); // Coral Orange
            
            // Light Theme Colors
            public static readonly Color LightBackground = Color.FromRgb(0xF8, 0xFA, 0xFC); // Very Light Blue-Gray
            public static readonly Color LightSurface = Colors.White;
            public static readonly Color LightBorder = Color.FromRgb(0xE1, 0xE8, 0xED); // Light Blue-Gray
            public static readonly Color LightText = Color.FromRgb(0x1F, 0x2D, 0x3D); // Dark Blue-Gray
            public static readonly Color LightTextSecondary = Color.FromRgb(0x64, 0x74, 0x8B); // Medium Blue-Gray
            
            // Dark Theme Colors
            public static readonly Color DarkBackground = Color.FromRgb(0x0D, 0x1B, 0x2A); // Very Dark Blue
            public static readonly Color DarkSurface = Color.FromRgb(0x16, 0x2A, 0x3E); // Dark Blue
            public static readonly Color DarkBorder = Color.FromRgb(0x2E, 0x4A, 0x66); // Medium Dark Blue
            public static readonly Color DarkText = Color.FromRgb(0xE1, 0xE8, 0xED); // Light Blue-Gray
            public static readonly Color DarkTextSecondary = Color.FromRgb(0xA0, 0xB4, 0xC8); // Medium Light Blue-Gray
        }

        public void SetTheme(bool isDarkMode)
        {
            IsDarkMode = isDarkMode;
            ApplyTheme();
        }

        public void ToggleTheme()
        {
            SetTheme(!IsDarkMode);
        }

        private void ApplyTheme()
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // Clear existing theme resources
            var keysToRemove = new[]
            {
                "AppBackgroundBrush", "AppSurfaceBrush", "AppBorderBrush",
                "AppTextBrush", "AppTextSecondaryBrush", "AppPrimaryBrush",
                "AppSecondaryBrush", "AppAccentBrush", "AppTentacleBrush", "AppCoralBrush"
            };

            foreach (var key in keysToRemove)
            {
                if (app.Resources.Contains(key))
                    app.Resources.Remove(key);
            }

            // Apply theme colors
            if (IsDarkMode)
            {
                app.Resources["AppBackgroundBrush"] = new SolidColorBrush(OctopiColors.DarkBackground);
                app.Resources["AppSurfaceBrush"] = new SolidColorBrush(OctopiColors.DarkSurface);
                app.Resources["AppBorderBrush"] = new SolidColorBrush(OctopiColors.DarkBorder);
                app.Resources["AppTextBrush"] = new SolidColorBrush(OctopiColors.DarkText);
                app.Resources["AppTextSecondaryBrush"] = new SolidColorBrush(OctopiColors.DarkTextSecondary);
            }
            else
            {
                app.Resources["AppBackgroundBrush"] = new SolidColorBrush(OctopiColors.LightBackground);
                app.Resources["AppSurfaceBrush"] = new SolidColorBrush(OctopiColors.LightSurface);
                app.Resources["AppBorderBrush"] = new SolidColorBrush(OctopiColors.LightBorder);
                app.Resources["AppTextBrush"] = new SolidColorBrush(OctopiColors.LightText);
                app.Resources["AppTextSecondaryBrush"] = new SolidColorBrush(OctopiColors.LightTextSecondary);
            }

            // Common colors (same for both themes)
            app.Resources["AppPrimaryBrush"] = new SolidColorBrush(OctopiColors.OctopiPrimary);
            app.Resources["AppSecondaryBrush"] = new SolidColorBrush(OctopiColors.OctopiSecondary);
            app.Resources["AppAccentBrush"] = new SolidColorBrush(OctopiColors.OctopiAccent);
            app.Resources["AppTentacleBrush"] = new SolidColorBrush(OctopiColors.OctopiTentacle);
            app.Resources["AppCoralBrush"] = new SolidColorBrush(OctopiColors.OctopiCoral);
        }

        public void InitializeTheme()
        {
            ApplyTheme();
        }

        // Equipment type colors with octopi theme
        public Brush GetEquipmentTypeBrush(string equipmentType)
        {
            return equipmentType switch
            {
                "ISP" => new SolidColorBrush(OctopiColors.OctopiPrimary),
                "Telco" => new SolidColorBrush(OctopiColors.OctopiSecondary),
                "CCTV" => new SolidColorBrush(OctopiColors.OctopiAccent),
                "Other" => new SolidColorBrush(OctopiColors.OctopiTentacle),
                _ => new SolidColorBrush(OctopiColors.OctopiCoral)
            };
        }

        // Status colors with octopi theme
        public Brush GetStatusBrush(string status)
        {
            return status switch
            {
                "Completed" => new SolidColorBrush(OctopiColors.OctopiAccent),
                "In Progress" => new SolidColorBrush(OctopiColors.OctopiSecondary),
                "On Hold" => new SolidColorBrush(OctopiColors.OctopiCoral),
                "Not Started" => new SolidColorBrush(OctopiColors.OctopiTentacle),
                _ => new SolidColorBrush(OctopiColors.OctopiPrimary)
            };
        }
    }
}
