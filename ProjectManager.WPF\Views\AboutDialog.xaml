<Window x:Class="ProjectManager.WPF.Views.AboutDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="About Octopi Project Manager"
        Height="600" Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        Icon="../octopi.ico">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>
        
        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
        
        <Style x:Key="BodyTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,3"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>

    <Grid Background="{DynamicResource AppSurfaceBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <StackPanel Grid.Row="0" Margin="30">
            <!-- Logo and Title -->
            <TextBlock Text="🐙" FontSize="48" HorizontalAlignment="Center" Margin="0,10"/>
            <TextBlock Text="Octopi Project Manager" Style="{StaticResource HeaderTextStyle}" Foreground="{DynamicResource AppPrimaryBrush}"/>
            <TextBlock Text="Project Tracker - Developed by Conrad Cloete" Style="{StaticResource SubHeaderTextStyle}" Foreground="{DynamicResource AppSecondaryBrush}"/>
            
            <!-- Version -->
            <TextBlock Text="Version 1.0" Style="{StaticResource SubHeaderTextStyle}" Margin="0,20,0,10"/>
            
            <!-- Description -->
            <TextBlock Text="A comprehensive project management solution designed specifically for Octopi Smart Solutions. Track projects, manage timelines, and visualize progress with beautiful Gantt charts."
                       Style="{StaticResource BodyTextStyle}"
                       Margin="0,10,0,20"
                       MaxWidth="400"/>
            
            <!-- Features -->
            <TextBlock Text="Features:" Style="{StaticResource SubHeaderTextStyle}" Margin="0,10,0,5"/>
            <StackPanel Margin="20,0">
                <TextBlock Text="• Project and team management" Style="{StaticResource BodyTextStyle}" HorizontalAlignment="Left"/>
                <TextBlock Text="• Interactive Gantt charts" Style="{StaticResource BodyTextStyle}" HorizontalAlignment="Left"/>
                <TextBlock Text="• CSV/Excel import and export" Style="{StaticResource BodyTextStyle}" HorizontalAlignment="Left"/>
                <TextBlock Text="• Dark and light themes" Style="{StaticResource BodyTextStyle}" HorizontalAlignment="Left"/>
                <TextBlock Text="• Comprehensive reporting" Style="{StaticResource BodyTextStyle}" HorizontalAlignment="Left"/>
            </StackPanel>
            
            <!-- Copyright -->
            <TextBlock Text="Designed by Conrad Cloete (AntmanZA) 2025"
                       Style="{StaticResource SubHeaderTextStyle}"
                       Foreground="{DynamicResource AppTentacleBrush}"
                       Margin="0,20,0,5"/>

            <TextBlock Text="Copyright © 2025 AntmanZa. All rights reserved."
                       Style="{StaticResource BodyTextStyle}"
                       Foreground="{DynamicResource AppTextSecondaryBrush}"/>
            
            <!-- Technology -->
            <TextBlock Text="Built with WPF, Entity Framework, and ❤️" 
                       Style="{StaticResource BodyTextStyle}" 
                       Foreground="{DynamicResource AppTextSecondaryBrush}"
                       Margin="0,10"/>
        </StackPanel>

        <!-- Close Button -->
        <Button Grid.Row="1" 
                Content="Close" 
                Width="100" 
                Height="35" 
                Margin="0,0,0,20"
                Background="{DynamicResource AppPrimaryBrush}" 
                Foreground="White" 
                BorderBrush="Transparent"
                Click="BtnClose_Click"/>
    </Grid>
</Window>
