# Create Octopi Project Manager Installer Package
Write-Host "Creating Octopi Project Manager Installer Package..." -ForegroundColor Green

# Remove existing ZIP if it exists
if (Test-Path "OctopiProjectManager_v1.0_Installer.zip") {
    Remove-Item "OctopiProjectManager_v1.0_Installer.zip" -Force
    Write-Host "Removed existing installer package." -ForegroundColor Yellow
}

# Create ZIP using .NET classes (more reliable)
Add-Type -AssemblyName System.IO.Compression.FileSystem

$sourceFolder = ".\OctopiProjectManager_Deployment"
$destinationZip = ".\OctopiProjectManager_v1.0_Installer.zip"

try {
    [System.IO.Compression.ZipFile]::CreateFromDirectory($sourceFolder, $destinationZip)
    Write-Host "Installer package created successfully!" -ForegroundColor Green
    
    # Show file info
    $zipInfo = Get-Item $destinationZip
    $sizeMB = [math]::Round($zipInfo.Length/1MB, 2)
    Write-Host "Package: $($zipInfo.Name)" -ForegroundColor Cyan
    Write-Host "Size: $sizeMB MB" -ForegroundColor Cyan
    Write-Host "Location: $($zipInfo.FullName)" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error creating installer package: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nDeployment package ready for distribution!" -ForegroundColor Green
