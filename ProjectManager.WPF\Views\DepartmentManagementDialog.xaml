<Window x:Class="ProjectManager.WPF.Views.DepartmentManagementDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Manage Departments" 
        Height="600" Width="800" 
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">

    <Window.Resources>
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="RowHeaderWidth" Value="0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
    </Window.Resources>

    <Grid Background="{DynamicResource AppSurfaceBrush}" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="🏢 Manage Departments" 
                   FontSize="20" FontWeight="Bold" 
                   Foreground="{DynamicResource AppPrimaryBrush}"
                   Margin="0,0,0,20"/>

        <!-- Toolbar -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
            <Button Name="btnAddDepartment" Content="➕ Add Department" 
                    Style="{StaticResource ButtonStyle}"
                    Background="{DynamicResource AppPrimaryBrush}" 
                    Foreground="White"
                    Click="BtnAddDepartment_Click"/>
            <Button Name="btnEditDepartment" Content="✏️ Edit" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#FFC107" Foreground="Black"
                    Click="BtnEditDepartment_Click"
                    IsEnabled="False"/>
            <Button Name="btnDeleteDepartment" Content="🗑️ Delete" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#DC3545" Foreground="White"
                    Click="BtnDeleteDepartment_Click"
                    IsEnabled="False"/>
            <Separator Width="20" Background="Transparent"/>
            <Button Name="btnRefresh" Content="🔄 Refresh" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#6C757D" Foreground="White"
                    Click="BtnRefresh_Click"/>
        </StackPanel>

        <!-- Department List -->
        <DataGrid Grid.Row="2" Name="departmentDataGrid" 
                  Style="{StaticResource DataGridStyle}"
                  SelectionChanged="DepartmentDataGrid_SelectionChanged">
            <DataGrid.Columns>
                <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>
                <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="200" IsReadOnly="True"/>
                <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="300" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="Color" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Rectangle Width="20" Height="20" 
                                          Fill="{Binding Color}" 
                                          Stroke="#CCCCCC" StrokeThickness="1"/>
                                <TextBlock Text="{Binding Color}" 
                                          VerticalAlignment="Center" 
                                          Margin="5,0,0,0" FontSize="10"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="Active" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToYesNoConverter}}" 
                                      HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Projects" Binding="{Binding ProjectCount}" Width="80" IsReadOnly="True"/>
                <DataGridTextColumn Header="Created" Binding="{Binding CreatedDate, StringFormat=yyyy-MM-dd}" Width="100" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status and Close -->
        <Grid Grid.Row="3" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Name="statusText" 
                       Text="Ready" VerticalAlignment="Center"
                       Foreground="{DynamicResource AppTextBrush}"/>
            
            <Button Grid.Column="1" Name="btnClose" Content="Close" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#6C757D" Foreground="White"
                    Click="BtnClose_Click"/>
        </Grid>
    </Grid>
</Window>
