<Project Sdk="WiX.SDK" DefaultTargets="Build">

  <PropertyGroup>
    <ProductVersion>1.0.0</ProductVersion>
    <ProjectGuid>12345678-1234-1234-1234-123456789013</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>OctopiProjectManagerInstaller</OutputName>
    <OutputType>Package</OutputType>
    <WixTargetsPath Condition=" '$(WixTargetsPath)' == '' ">$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets</WixTargetsPath>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="OctopiProjectManager.wxs" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="License.rtf" />
  </ItemGroup>

</Project>
