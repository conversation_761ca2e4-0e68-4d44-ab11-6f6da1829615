@echo off
echo.
echo ========================================
echo   Octopi Project Manager v1.1 Uninstaller
echo   Developed by <PERSON> (AntmanZA)
echo ========================================
echo.

set /p confirm="Are you sure you want to uninstall Octopi Project Manager? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Uninstall cancelled.
    pause
    exit /b
)

echo.
echo Uninstalling Octopi Project Manager...
echo.

REM Remove shortcuts
echo Removing shortcuts...
del "%USERPROFILE%\Desktop\Octopi Project Manager.lnk" 2>nul
rmdir /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Octopi Project Manager" 2>nul

REM Remove registry entry
echo Removing registry entries...
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Octopi Project Manager" /f 2>nul

echo.
echo ========================================
echo Octopi Project Manager has been uninstalled successfully!
echo.
echo Note: Your project data has been preserved and can be
echo used if you reinstall the application.
echo ========================================
echo.
pause
