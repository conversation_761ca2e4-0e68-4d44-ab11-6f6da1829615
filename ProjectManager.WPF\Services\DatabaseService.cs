using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Services
{
    public class DatabaseService
    {
        private readonly ProjectManagerDbContext _context;

        public DatabaseService(ProjectManagerDbContext context)
        {
            _context = context;
        }

        // Team operations
        public async Task<List<Team>> GetAllTeamsAsync()
        {
            return await _context.Teams
                .Where(t => t.IsActive)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<Team?> GetTeamByIdAsync(int id)
        {
            return await _context.Teams.FindAsync(id);
        }

        public async Task<Team> AddTeamAsync(Team team)
        {
            _context.Teams.Add(team);
            await _context.SaveChangesAsync();
            return team;
        }

        public async Task<Team> UpdateTeamAsync(Team team)
        {
            _context.Teams.Update(team);
            await _context.SaveChangesAsync();
            return team;
        }

        public async Task DeleteTeamAsync(int id)
        {
            var team = await _context.Teams.FindAsync(id);
            if (team != null)
            {
                team.IsActive = false; // Soft delete
                await _context.SaveChangesAsync();
            }
        }

        // Project operations
        public async Task<List<Project>> GetAllProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddProjectAsync(Project project)
        {
            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            project.ModifiedDate = DateTime.Now;
            _context.Projects.Update(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task DeleteProjectAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project != null)
            {
                _context.Projects.Remove(project);
                await _context.SaveChangesAsync();
            }
        }

        // ProjectTask operations
        public async Task<List<ProjectTask>> GetTasksByProjectIdAsync(int projectId)
        {
            return await _context.ProjectTasks
                .Include(pt => pt.Team)
                .Where(pt => pt.ProjectId == projectId)
                .OrderBy(pt => pt.StartDate)
                .ToListAsync();
        }

        public async Task<ProjectTask?> GetTaskByIdAsync(int id)
        {
            return await _context.ProjectTasks
                .Include(pt => pt.Project)
                .Include(pt => pt.Team)
                .FirstOrDefaultAsync(pt => pt.Id == id);
        }

        public async Task<ProjectTask> AddTaskAsync(ProjectTask task)
        {
            _context.ProjectTasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task<ProjectTask> UpdateTaskAsync(ProjectTask task)
        {
            task.ModifiedDate = DateTime.Now;
            _context.ProjectTasks.Update(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task DeleteTaskAsync(int id)
        {
            var task = await _context.ProjectTasks.FindAsync(id);
            if (task != null)
            {
                _context.ProjectTasks.Remove(task);
                await _context.SaveChangesAsync();
            }
        }

        // Filtering and search operations
        public async Task<List<Project>> GetProjectsByTeamAsync(int teamId)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.TeamId == teamId)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> GetProjectsByEquipmentTypeAsync(string equipmentType)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.EquipmentType == equipmentType)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> GetProjectsByStatusAsync(string status)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.Status == status)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> SearchProjectsAsync(string searchTerm)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.Name.Contains(searchTerm) || 
                           p.Description!.Contains(searchTerm) ||
                           p.Team.Name.Contains(searchTerm))
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        // Database initialization
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                // Ensure database is created
                var created = await _context.Database.EnsureCreatedAsync();

                if (created)
                {
                    System.Diagnostics.Debug.WriteLine("Database created successfully");
                }

                // Test basic database operations
                await _context.Database.CanConnectAsync();

                // Check if we need to add new columns (simple migration)
                await MigrateToNewSchemaAsync();

                // Verify essential data exists
                await VerifyEssentialDataAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database initialization failed: {ex.Message}", ex);
            }
        }

        private async Task VerifyEssentialDataAsync()
        {
            try
            {
                // Check if we have any teams
                var teamCount = await _context.Teams.CountAsync();
                if (teamCount == 0)
                {
                    // Add default teams if none exist
                    var defaultTeams = new List<Team>();
                    for (int i = 1; i <= 5; i++)
                    {
                        defaultTeams.Add(new Team
                        {
                            Name = $"Team {i}",
                            Description = $"Installation Team {i}",
                            IsActive = true,
                            CreatedDate = DateTime.Now
                        });
                    }

                    _context.Teams.AddRange(defaultTeams);
                    await _context.SaveChangesAsync();
                }

                // Check if we have any departments
                var deptCount = await _context.Departments.CountAsync();
                if (deptCount == 0)
                {
                    var defaultDepartments = new List<DepartmentEntity>
                    {
                        new DepartmentEntity { Name = "ISP", Description = "Internet Service Provider installations", Color = "#5E81AC", IsActive = true, CreatedDate = DateTime.Now },
                        new DepartmentEntity { Name = "Telco", Description = "Telecommunications installations", Color = "#88C0D0", IsActive = true, CreatedDate = DateTime.Now },
                        new DepartmentEntity { Name = "CCTV", Description = "Security camera installations", Color = "#A3BE8C", IsActive = true, CreatedDate = DateTime.Now },
                        new DepartmentEntity { Name = "Other", Description = "Other types of installations", Color = "#EBCB8B", IsActive = true, CreatedDate = DateTime.Now }
                    };

                    _context.Departments.AddRange(defaultDepartments);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error verifying essential data: {ex.Message}");
                // Don't throw here - this is not critical for app startup
            }
        }

        private async Task MigrateToNewSchemaAsync()
        {
            var connection = _context.Database.GetDbConnection();
            try
            {
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                using var command = connection.CreateCommand();

                // Check if Departments table exists
                command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='Departments'";
                var departmentsTableExists = await command.ExecuteScalarAsync() != null;

                if (!departmentsTableExists)
                {
                    // Create Departments table
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS Departments (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            Name TEXT NOT NULL UNIQUE,
                            Description TEXT,
                            Color TEXT DEFAULT '#5E81AC',
                            IsActive INTEGER NOT NULL DEFAULT 1,
                            CreatedDate TEXT NOT NULL
                        )";
                    await command.ExecuteNonQueryAsync();
                    System.Diagnostics.Debug.WriteLine("Departments table created");
                }

                // Check if Projects table has required columns
                command.CommandText = "PRAGMA table_info(Projects)";
                using var reader = await command.ExecuteReaderAsync();
                var columns = new List<string>();

                while (await reader.ReadAsync())
                {
                    columns.Add(reader.GetString(1)); // Column name is at index 1
                }

                await reader.CloseAsync();

                // Add missing columns to Projects table one by one
                var columnsToAdd = new[]
                {
                    ("ProjectRef", "TEXT"),
                    ("CustomerName", "TEXT"),
                    ("DepartmentId", "INTEGER")
                };

                foreach (var (columnName, columnType) in columnsToAdd)
                {
                    if (!columns.Contains(columnName))
                    {
                        try
                        {
                            command.CommandText = $"ALTER TABLE Projects ADD COLUMN {columnName} {columnType}";
                            await command.ExecuteNonQueryAsync();
                            System.Diagnostics.Debug.WriteLine($"Added column {columnName} to Projects table");
                        }
                        catch (Exception colEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to add column {columnName}: {colEx.Message}");
                            // Continue with other columns
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Migration error: {ex.Message}");
                throw new InvalidOperationException($"Database migration failed: {ex.Message}", ex);
            }
            finally
            {
                if (connection.State == System.Data.ConnectionState.Open)
                {
                    await connection.CloseAsync();
                }
            }
        }
    }
}
