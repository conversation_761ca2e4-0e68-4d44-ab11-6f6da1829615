using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Services
{
    public class DatabaseService
    {
        private readonly ProjectManagerDbContext _context;

        public DatabaseService(ProjectManagerDbContext context)
        {
            _context = context;
        }

        // Team operations
        public async Task<List<Team>> GetAllTeamsAsync()
        {
            return await _context.Teams
                .Where(t => t.IsActive)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<Team?> GetTeamByIdAsync(int id)
        {
            return await _context.Teams.FindAsync(id);
        }

        public async Task<Team> AddTeamAsync(Team team)
        {
            _context.Teams.Add(team);
            await _context.SaveChangesAsync();
            return team;
        }

        public async Task<Team> UpdateTeamAsync(Team team)
        {
            _context.Teams.Update(team);
            await _context.SaveChangesAsync();
            return team;
        }

        public async Task DeleteTeamAsync(int id)
        {
            var team = await _context.Teams.FindAsync(id);
            if (team != null)
            {
                team.IsActive = false; // Soft delete
                await _context.SaveChangesAsync();
            }
        }

        // Project operations
        public async Task<List<Project>> GetAllProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddProjectAsync(Project project)
        {
            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            project.ModifiedDate = DateTime.Now;
            _context.Projects.Update(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task DeleteProjectAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project != null)
            {
                _context.Projects.Remove(project);
                await _context.SaveChangesAsync();
            }
        }

        // ProjectTask operations
        public async Task<List<ProjectTask>> GetTasksByProjectIdAsync(int projectId)
        {
            return await _context.ProjectTasks
                .Include(pt => pt.Team)
                .Where(pt => pt.ProjectId == projectId)
                .OrderBy(pt => pt.StartDate)
                .ToListAsync();
        }

        public async Task<ProjectTask?> GetTaskByIdAsync(int id)
        {
            return await _context.ProjectTasks
                .Include(pt => pt.Project)
                .Include(pt => pt.Team)
                .FirstOrDefaultAsync(pt => pt.Id == id);
        }

        public async Task<ProjectTask> AddTaskAsync(ProjectTask task)
        {
            _context.ProjectTasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task<ProjectTask> UpdateTaskAsync(ProjectTask task)
        {
            task.ModifiedDate = DateTime.Now;
            _context.ProjectTasks.Update(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task DeleteTaskAsync(int id)
        {
            var task = await _context.ProjectTasks.FindAsync(id);
            if (task != null)
            {
                _context.ProjectTasks.Remove(task);
                await _context.SaveChangesAsync();
            }
        }

        // Filtering and search operations
        public async Task<List<Project>> GetProjectsByTeamAsync(int teamId)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.TeamId == teamId)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> GetProjectsByEquipmentTypeAsync(string equipmentType)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.EquipmentType == equipmentType)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> GetProjectsByStatusAsync(string status)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.Status == status)
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<List<Project>> SearchProjectsAsync(string searchTerm)
        {
            return await _context.Projects
                .Include(p => p.Team)
                .Include(p => p.Tasks)
                .Where(p => p.Name.Contains(searchTerm) || 
                           p.Description!.Contains(searchTerm) ||
                           p.Team.Name.Contains(searchTerm))
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        // Database initialization
        public async Task InitializeDatabaseAsync()
        {
            await _context.Database.EnsureCreatedAsync();

            // Check if we need to add new columns (simple migration)
            await MigrateToNewSchemaAsync();
        }

        private async Task MigrateToNewSchemaAsync()
        {
            try
            {
                // Check if ProjectRef column exists
                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Projects)";

                var reader = await command.ExecuteReaderAsync();
                var columns = new List<string>();

                while (await reader.ReadAsync())
                {
                    columns.Add(reader.GetString(1)); // Column name is at index 1
                }

                await reader.CloseAsync();

                // Add missing columns
                if (!columns.Contains("ProjectRef"))
                {
                    command.CommandText = "ALTER TABLE Projects ADD COLUMN ProjectRef TEXT";
                    await command.ExecuteNonQueryAsync();
                }

                if (!columns.Contains("CustomerName"))
                {
                    command.CommandText = "ALTER TABLE Projects ADD COLUMN CustomerName TEXT";
                    await command.ExecuteNonQueryAsync();
                }

                await connection.CloseAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Migration error: {ex.Message}");
                // Continue anyway - the columns might already exist
            }
        }
    }
}
