<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>..\..\octopi.ico</ApplicationIcon>
    <AssemblyTitle>Octopi Project Manager Installer</AssemblyTitle>
    <AssemblyDescription>Standalone installer for Octopi Project Manager v1.1</AssemblyDescription>
    <AssemblyCompany><PERSON> (AntmanZA)</AssemblyCompany>
    <AssemblyProduct>Octopi Project Manager</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025 AntmanZa</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="..\..\OctopiProjectManager_v1.1_Working_Installer.zip">
      <LogicalName>OctopiStandaloneInstaller.OctopiProjectManager.zip</LogicalName>
    </EmbeddedResource>
  </ItemGroup>

</Project>
