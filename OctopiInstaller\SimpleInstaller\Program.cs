using System;
using System.IO;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using Microsoft.Win32;

namespace OctopiProjectManagerInstaller
{
    class Program
    {
        private static string AppName = "Octopi Project Manager";
        private static string AppVersion = "1.1.0";
        private static string Publisher = "Conrad C<PERSON>ete (AntmanZA)";
        private static string InstallPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "OctopiProjectManager");
        private static string AppExe = "ProjectManager.WPF.exe";

        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Check if running as administrator
            if (!IsRunningAsAdministrator())
            {
                MessageBox.Show("This installer requires administrator privileges.\nPlease run as administrator.", 
                    "Administrator Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Check for uninstall argument
            if (args.Length > 0 && args[0].ToLower() == "/uninstall")
            {
                UninstallApplication();
                return;
            }

            // Show installer form
            Application.Run(new InstallerForm());
        }

        public static bool IsRunningAsAdministrator()
        {
            var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            var principal = new System.Security.Principal.WindowsPrincipal(identity);
            return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
        }

        public static bool InstallApplication(string targetPath, bool createDesktop, bool createStartMenu, IProgress<string> progress)
        {
            try
            {
                progress?.Report("Creating installation directory...");
                
                // Create installation directory
                if (!Directory.Exists(targetPath))
                {
                    Directory.CreateDirectory(targetPath);
                }

                progress?.Report("Extracting application files...");

                // Copy application files from current directory or extract embedded
                string sourceExe = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AppExe);
                if (File.Exists(sourceExe))
                {
                    File.Copy(sourceExe, Path.Combine(targetPath, AppExe), true);

                    // Also copy any additional files in the same directory
                    var sourceDir = AppDomain.CurrentDomain.BaseDirectory;
                    var additionalFiles = new[] { "ProjectManager.WPF.pdb", "ProjectManager.WPF.runtimeconfig.json", "ProjectManager.WPF.deps.json" };

                    foreach (var file in additionalFiles)
                    {
                        var sourceFile = Path.Combine(sourceDir, file);
                        if (File.Exists(sourceFile))
                        {
                            File.Copy(sourceFile, Path.Combine(targetPath, file), true);
                        }
                    }
                }
                else
                {
                    // Try to extract from embedded resource
                    ExtractEmbeddedResource("OctopiProjectManagerInstaller.ProjectManager.WPF.exe",
                        Path.Combine(targetPath, AppExe));
                }

                progress?.Report("Creating shortcuts...");

                // Create desktop shortcut
                if (createDesktop)
                {
                    CreateShortcut(
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), $"{AppName}.lnk"),
                        Path.Combine(targetPath, AppExe),
                        targetPath,
                        $"{AppName} - Project Tracker"
                    );
                }

                // Create start menu shortcut
                if (createStartMenu)
                {
                    string startMenuPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), 
                        AppName);
                    
                    if (!Directory.Exists(startMenuPath))
                    {
                        Directory.CreateDirectory(startMenuPath);
                    }

                    CreateShortcut(
                        Path.Combine(startMenuPath, $"{AppName}.lnk"),
                        Path.Combine(targetPath, AppExe),
                        targetPath,
                        $"{AppName} - Project Tracker"
                    );
                }

                progress?.Report("Registering application...");

                // Register in Add/Remove Programs
                RegisterApplication(targetPath);

                progress?.Report("Installation completed successfully!");
                return true;
            }
            catch (Exception ex)
            {
                progress?.Report($"Installation failed: {ex.Message}");
                return false;
            }
        }

        private static void ExtractEmbeddedResource(string resourceName, string outputPath)
        {
            var assembly = Assembly.GetExecutingAssembly();
            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                {
                    throw new FileNotFoundException($"Embedded resource '{resourceName}' not found.");
                }

                using (var fileStream = File.Create(outputPath))
                {
                    stream.CopyTo(fileStream);
                }
            }
        }

        private static void CreateShortcut(string shortcutPath, string targetPath, string workingDirectory, string description)
        {
            try
            {
                var shell = Activator.CreateInstance(Type.GetTypeFromProgID("WScript.Shell"));
                var shortcut = shell.GetType().InvokeMember("CreateShortcut", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shell, new object[] { shortcutPath });

                shortcut.GetType().InvokeMember("TargetPath", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { targetPath });
                shortcut.GetType().InvokeMember("WorkingDirectory", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { workingDirectory });
                shortcut.GetType().InvokeMember("Description", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { description });
                shortcut.GetType().InvokeMember("Save", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shortcut, null);
            }
            catch (Exception ex)
            {
                // Shortcut creation failed, but don't fail the entire installation
                Console.WriteLine($"Warning: Could not create shortcut: {ex.Message}");
            }
        }

        private static void RegisterApplication(string installPath)
        {
            try
            {
                string uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                using (RegistryKey key = Registry.LocalMachine.CreateSubKey(uninstallKey))
                {
                    key.SetValue("DisplayName", AppName);
                    key.SetValue("DisplayVersion", AppVersion);
                    key.SetValue("Publisher", Publisher);
                    key.SetValue("InstallLocation", installPath);
                    key.SetValue("DisplayIcon", Path.Combine(installPath, AppExe));
                    key.SetValue("UninstallString", $"\"{Assembly.GetExecutingAssembly().Location}\" /uninstall");
                    key.SetValue("EstimatedSize", 160000, RegistryValueKind.DWord);
                    key.SetValue("NoModify", 1, RegistryValueKind.DWord);
                    key.SetValue("NoRepair", 1, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to register application: {ex.Message}");
            }
        }

        public static void UninstallApplication()
        {
            try
            {
                // Show detailed uninstall confirmation
                var confirmResult = MessageBox.Show(
                    $"This will completely remove {AppName} v{AppVersion} from your computer.\n\n" +
                    "The following will be removed:\n" +
                    "• Application files\n" +
                    "• Desktop and Start Menu shortcuts\n" +
                    "• Registry entries\n" +
                    "• Add/Remove Programs entry\n\n" +
                    "Note: Your project data and database files will be preserved.\n\n" +
                    "Are you sure you want to continue?",
                    "Uninstall Octopi Project Manager",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (confirmResult != DialogResult.Yes)
                    return;

                // Show uninstall progress form
                var uninstallForm = new UninstallProgressForm();
                uninstallForm.Show();
                Application.DoEvents();

                // Step 1: Remove shortcuts
                uninstallForm.UpdateProgress("Removing shortcuts...", 25);

                string desktopShortcut = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), $"{AppName}.lnk");
                if (File.Exists(desktopShortcut))
                {
                    File.Delete(desktopShortcut);
                    uninstallForm.UpdateProgress("Desktop shortcut removed", 35);
                }

                string startMenuPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), AppName);
                if (Directory.Exists(startMenuPath))
                {
                    Directory.Delete(startMenuPath, true);
                    uninstallForm.UpdateProgress("Start Menu shortcuts removed", 45);
                }

                // Step 2: Remove registry entries
                uninstallForm.UpdateProgress("Removing registry entries...", 60);

                string uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                try
                {
                    Registry.LocalMachine.DeleteSubKey(uninstallKey, false);
                    uninstallForm.UpdateProgress("Registry entries removed", 75);
                }
                catch
                {
                    // Registry key might not exist, continue
                    uninstallForm.UpdateProgress("Registry cleanup completed", 75);
                }

                // Step 3: Remove application files
                uninstallForm.UpdateProgress("Removing application files...", 85);

                if (Directory.Exists(InstallPath))
                {
                    // Try to remove files, but preserve database files
                    var filesToRemove = Directory.GetFiles(InstallPath, "*.*", SearchOption.AllDirectories)
                        .Where(f => !f.EndsWith(".db") && !f.EndsWith(".db-journal") && !f.EndsWith(".db-wal"))
                        .ToArray();

                    foreach (var file in filesToRemove)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // File might be in use, skip it
                        }
                    }

                    // Remove empty directories
                    try
                    {
                        var emptyDirs = Directory.GetDirectories(InstallPath, "*", SearchOption.AllDirectories)
                            .Where(d => !Directory.EnumerateFileSystemEntries(d).Any());

                        foreach (var dir in emptyDirs)
                        {
                            try
                            {
                                Directory.Delete(dir);
                            }
                            catch { }
                        }

                        // Try to remove main directory if empty
                        if (!Directory.EnumerateFileSystemEntries(InstallPath).Any())
                        {
                            Directory.Delete(InstallPath);
                        }
                    }
                    catch { }
                }

                uninstallForm.UpdateProgress("Uninstall completed successfully!", 100);
                System.Threading.Thread.Sleep(1000);
                uninstallForm.Close();

                MessageBox.Show(
                    $"{AppName} has been uninstalled successfully!\n\n" +
                    "Your project data has been preserved and can be used if you reinstall the application.",
                    "Uninstall Complete",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uninstall failed: {ex.Message}",
                    "Uninstall Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
