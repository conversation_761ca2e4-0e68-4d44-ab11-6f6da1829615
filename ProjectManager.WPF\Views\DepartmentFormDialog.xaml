<Window x:Class="ProjectManager.WPF.Views.DepartmentFormDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Department Details" 
        Height="400" Width="450" 
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">

    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Background="{DynamicResource AppSurfaceBrush}" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Name="headerText" Text="Add New Department" 
                   FontSize="18" FontWeight="Bold" 
                   Foreground="{DynamicResource AppPrimaryBrush}"
                   Margin="0,0,0,20"/>

        <!-- Form Fields -->
        <StackPanel Grid.Row="1">
            <!-- Department Name -->
            <TextBlock Text="Department Name *" Style="{StaticResource LabelStyle}"/>
            <TextBox Name="txtDepartmentName" Style="{StaticResource TextBoxStyle}" 
                     MaxLength="100" TabIndex="1"/>

            <!-- Description -->
            <TextBlock Text="Description" Style="{StaticResource LabelStyle}"/>
            <TextBox Name="txtDescription" Style="{StaticResource TextBoxStyle}" 
                     MaxLength="500" Height="60" TextWrapping="Wrap" 
                     AcceptsReturn="True" VerticalScrollBarVisibility="Auto" TabIndex="2"/>

            <!-- Color -->
            <TextBlock Text="Color" Style="{StaticResource LabelStyle}"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                <TextBox Name="txtColor" Style="{StaticResource TextBoxStyle}" 
                         Width="100" MaxLength="7" Text="#5E81AC" TabIndex="3"/>
                <Rectangle Name="colorPreview" Width="30" Height="30" 
                           Fill="#5E81AC" Stroke="#CCCCCC" StrokeThickness="1" 
                           Margin="10,0,0,0"/>
                <Button Name="btnPickColor" Content="Pick Color" 
                        Style="{StaticResource ButtonStyle}"
                        Background="#E0E0E0" Foreground="Black"
                        Click="BtnPickColor_Click" TabIndex="4"/>
            </StackPanel>

            <!-- Active Status -->
            <CheckBox Name="chkIsActive" Content="Active" 
                      IsChecked="True" Margin="0,0,0,15" TabIndex="5"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="btnSave" Content="Save" 
                    Style="{StaticResource ButtonStyle}"
                    Background="{DynamicResource AppPrimaryBrush}" 
                    Foreground="White"
                    Click="BtnSave_Click" TabIndex="6"/>
            <Button Name="btnCancel" Content="Cancel" 
                    Style="{StaticResource ButtonStyle}"
                    Background="#6C757D" Foreground="White"
                    Click="BtnCancel_Click" TabIndex="7"/>
        </StackPanel>
    </Grid>
</Window>
