﻿#pragma checksum "..\..\..\..\Views\CsvImportDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9CF1ADDEEC35D9BF31B05AD403DE83B6223597FB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ProjectManager.WPF.Views {
    
    
    /// <summary>
    /// CsvImportDialog
    /// </summary>
    public partial class CsvImportDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 50 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl importTabControl;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem tabFileSelection;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtFilePath;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBrowseFile;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDownloadTemplate;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid previewDataGrid;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem tabColumnMapping;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel mappingPanel;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem tabImport;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnValidate;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock validationSummary;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid validationDataGrid;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSkipErrors;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnStartImport;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrevious;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNext;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\CsvImportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ProjectManager.WPF;component/views/csvimportdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CsvImportDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.importTabControl = ((System.Windows.Controls.TabControl)(target));
            
            #line 50 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.importTabControl.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ImportTabControl_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.tabFileSelection = ((System.Windows.Controls.TabItem)(target));
            return;
            case 3:
            this.txtFilePath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.btnBrowseFile = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnBrowseFile.Click += new System.Windows.RoutedEventHandler(this.BtnBrowseFile_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnDownloadTemplate = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnDownloadTemplate.Click += new System.Windows.RoutedEventHandler(this.BtnDownloadTemplate_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.previewDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 7:
            this.tabColumnMapping = ((System.Windows.Controls.TabItem)(target));
            return;
            case 8:
            this.mappingPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.tabImport = ((System.Windows.Controls.TabItem)(target));
            return;
            case 10:
            this.btnValidate = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnValidate.Click += new System.Windows.RoutedEventHandler(this.BtnValidate_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.validationSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.validationDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.chkSkipErrors = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.btnStartImport = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnStartImport.Click += new System.Windows.RoutedEventHandler(this.BtnStartImport_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnPrevious = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnPrevious.Click += new System.Windows.RoutedEventHandler(this.BtnPrevious_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnNext = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnNext.Click += new System.Windows.RoutedEventHandler(this.BtnNext_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\Views\CsvImportDialog.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

