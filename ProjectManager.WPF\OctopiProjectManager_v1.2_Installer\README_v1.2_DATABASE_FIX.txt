OCTOPI PROJECT MANAGER v1.2 - DATABASE INITIALIZATION FIX
===========================================================

WHAT'S FIXED IN v1.2:
- Fixed database initialization error that occurred when running the installed application
- Improved error handling for database connection issues
- Added fallback database path options for different installation scenarios
- Enhanced database migration process with better error recovery
- Added timeout protection for database operations
- Improved connection string handling for SQLite database

INSTALLATION INSTRUCTIONS:
1. Extract all files to a folder of your choice (e.g., C:\OctopiProjectManager)
2. Run ProjectManager.WPF.exe to start the application
3. The application will automatically create the database in your Documents folder
4. If Documents folder is not accessible, it will create the database in the application folder

FEATURES:
- Project management with teams and departments
- CSV import functionality for project data
- Dark/Light theme support
- Gantt chart visualization
- Excel export capabilities
- Department management with color coding
- Bulk project operations

SYSTEM REQUIREMENTS:
- Windows 10 or later
- .NET 6.0 Runtime (included in this standalone package)
- Minimum 4GB RAM
- 500MB free disk space

TROUBLESHOOTING:
If you encounter any database errors:
1. Close the application
2. Delete the ProjectManager.db file from your Documents\ProjectManagementApp folder
3. Restart the application - it will recreate the database

SUPPORT:
For technical support, contact: Conrad Cloete (AntmanZA)
Copyright 2025 - Octopi Smart Solutions

VERSION HISTORY:
v1.2 - Fixed database initialization errors
v1.1 - Added department management and CSV import
v1.0 - Initial release
