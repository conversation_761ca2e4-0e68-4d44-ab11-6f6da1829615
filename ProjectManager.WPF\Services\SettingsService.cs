using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace ProjectManager.WPF.Services
{
    public class SettingsService
    {
        private readonly string _settingsPath;
        private AppSettings _settings;

        public SettingsService()
        {
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "ProjectManagementApp");
            
            Directory.CreateDirectory(appDataPath);
            _settingsPath = Path.Combine(appDataPath, "settings.json");
            
            _settings = new AppSettings();
            LoadSettings();
        }

        public AppSettings Settings => _settings;

        public async Task SaveSettingsAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                await File.WriteAllTextAsync(_settingsPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    _settings = JsonSerializer.Deserialize<AppSettings>(json) ?? new AppSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
                _settings = new AppSettings();
            }
        }

        public async Task UpdateSettingAsync<T>(string key, T value)
        {
            switch (key.ToLower())
            {
                case "darkmode":
                    _settings.DarkMode = Convert.ToBoolean(value);
                    break;
                case "autosave":
                    _settings.AutoSave = Convert.ToBoolean(value);
                    break;
                case "shownotifications":
                    _settings.ShowNotifications = Convert.ToBoolean(value);
                    break;
                case "defaultequipmenttype":
                    _settings.DefaultEquipmentType = value?.ToString() ?? string.Empty;
                    break;
                case "defaultprojectstatus":
                    _settings.DefaultProjectStatus = value?.ToString() ?? string.Empty;
                    break;
            }
            
            await SaveSettingsAsync();
        }
    }

    public class AppSettings
    {
        public bool DarkMode { get; set; } = false;
        public bool AutoSave { get; set; } = true;
        public bool ShowNotifications { get; set; } = true;
        public string DefaultEquipmentType { get; set; } = "ISP";
        public string DefaultProjectStatus { get; set; } = "Not Started";
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
