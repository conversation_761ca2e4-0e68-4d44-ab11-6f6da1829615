using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Media;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Views
{
    public partial class DepartmentFormDialog : Window
    {
        public Department? Department { get; private set; }
        public bool IsEditMode { get; private set; }

        public DepartmentFormDialog(Department? department = null)
        {
            InitializeComponent();
            
            if (department != null)
            {
                IsEditMode = true;
                Department = department;
                LoadDepartmentData();
                headerText.Text = "Edit Department";
                Title = "Edit Department";
            }
            else
            {
                IsEditMode = false;
                Department = new Department();
                headerText.Text = "Add New Department";
                Title = "Add New Department";
            }

            // Set up color change event
            txtColor.TextChanged += TxtColor_TextChanged;
            
            // Focus on name field
            txtDepartmentName.Focus();
        }

        private void LoadDepartmentData()
        {
            if (Department == null) return;

            txtDepartmentName.Text = Department.Name;
            txtDescription.Text = Department.Description ?? string.Empty;
            txtColor.Text = Department.Color ?? "#5E81AC";
            chkIsActive.IsChecked = Department.IsActive;
            
            UpdateColorPreview();
        }

        private void TxtColor_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateColorPreview();
        }

        private void UpdateColorPreview()
        {
            try
            {
                var colorText = txtColor.Text.Trim();
                if (IsValidHexColor(colorText))
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorText);
                    colorPreview.Fill = new SolidColorBrush(color);
                }
                else
                {
                    colorPreview.Fill = new SolidColorBrush(Colors.Gray);
                }
            }
            catch
            {
                colorPreview.Fill = new SolidColorBrush(Colors.Gray);
            }
        }

        private bool IsValidHexColor(string color)
        {
            if (string.IsNullOrWhiteSpace(color)) return false;
            
            // Check if it's a valid hex color format
            var hexPattern = @"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$";
            return Regex.IsMatch(color, hexPattern);
        }

        private void BtnPickColor_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Use Windows Forms ColorDialog for color picking
                using var colorDialog = new System.Windows.Forms.ColorDialog();
                
                // Set current color if valid
                if (IsValidHexColor(txtColor.Text))
                {
                    var currentColor = (Color)ColorConverter.ConvertFromString(txtColor.Text);
                    colorDialog.Color = System.Drawing.Color.FromArgb(currentColor.A, currentColor.R, currentColor.G, currentColor.B);
                }

                if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    var selectedColor = colorDialog.Color;
                    txtColor.Text = $"#{selectedColor.R:X2}{selectedColor.G:X2}{selectedColor.B:X2}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening color picker: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                SaveDepartment();
                DialogResult = true;
                Close();
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            // Validate department name
            if (string.IsNullOrWhiteSpace(txtDepartmentName.Text))
            {
                MessageBox.Show("Department name is required.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtDepartmentName.Focus();
                return false;
            }

            if (txtDepartmentName.Text.Length > 100)
            {
                MessageBox.Show("Department name cannot exceed 100 characters.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtDepartmentName.Focus();
                return false;
            }

            // Validate description length
            if (!string.IsNullOrEmpty(txtDescription.Text) && txtDescription.Text.Length > 500)
            {
                MessageBox.Show("Description cannot exceed 500 characters.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtDescription.Focus();
                return false;
            }

            // Validate color
            if (!IsValidHexColor(txtColor.Text))
            {
                MessageBox.Show("Please enter a valid hex color (e.g., #5E81AC).", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtColor.Focus();
                return false;
            }

            return true;
        }

        private void SaveDepartment()
        {
            if (Department == null) return;

            Department.Name = txtDepartmentName.Text.Trim();
            Department.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();
            Department.Color = txtColor.Text.Trim();
            Department.IsActive = chkIsActive.IsChecked ?? true;

            if (!IsEditMode)
            {
                Department.CreatedDate = DateTime.Now;
            }
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Apply theme
            var themeManager = Services.ThemeManager.Instance;
            if (themeManager.IsDarkMode)
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E3440"));
            }
        }
    }
}
