﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>octopi.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="EPPlus" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.36">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Resource Include="octopi.ico" />
  </ItemGroup>

</Project>
