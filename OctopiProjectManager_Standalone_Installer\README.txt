========================================
    OCTOPI PROJECT MANAGER v1.0
    STANDALONE EXECUTABLE INSTALLER
    Developed by <PERSON> (AntmanZA)
========================================

ABOUT THIS INSTALLER:
---------------------
This is a professional standalone executable installer that does NOT require 
running any scripts or PowerShell commands. It's a true Windows executable 
(.exe) that provides a graphical installation experience.

FEATURES:
---------
✅ TRUE EXECUTABLE INSTALLER - No scripts required
✅ Professional GUI interface with progress tracking
✅ Custom installation path selection
✅ Automatic shortcut creation (Desktop & Start Menu)
✅ Add/Remove Programs integration
✅ Clean uninstaller included
✅ Administrator privilege handling
✅ Self-contained - includes all necessary files

INSTALLATION:
------------
1. Right-click "OctopiProjectManagerSetup.exe"
2. Select "Run as administrator"
3. Follow the graphical installation wizard
4. Choose your installation preferences
5. Click "Install" and wait for completion

SYSTEM REQUIREMENTS:
-------------------
• Windows 10 or later (64-bit)
• Administrator privileges for installation
• 200MB free disk space
• No additional software required

WHAT GETS INSTALLED:
-------------------
• Main application: ProjectManager.WPF.exe
• Desktop shortcut (optional)
• Start Menu shortcuts (optional)
• Add/Remove Programs entry
• Uninstaller registration

UNINSTALLATION:
--------------
• Use "Add or Remove Programs" in Windows Settings
• Or run: OctopiProjectManagerSetup.exe /uninstall

ADVANTAGES OF THIS INSTALLER:
----------------------------
✅ No PowerShell execution policy issues
✅ No script security warnings
✅ Professional Windows installer experience
✅ Works on locked-down corporate environments
✅ Single executable file - easy to distribute
✅ Embedded application files - nothing to lose
✅ Standard Windows installer behavior

TECHNICAL DETAILS:
-----------------
• Built with .NET 6 Windows Forms
• Self-contained executable (300.62 MB)
• Includes embedded application files
• Professional installer UI
• Registry integration for proper uninstall
• Shortcut creation using Windows Shell

DEPLOYMENT:
----------
This installer is perfect for:
• Corporate environments with script restrictions
• Mass deployment via Group Policy
• Software distribution systems
• End-user installations
• IT administrator deployments

SUPPORT:
--------
Developer: Conrad Cloete (AntmanZA)
Copyright: © 2025 AntmanZa
Version: 1.0.0

========================================
PROFESSIONAL EXECUTABLE INSTALLER
NO SCRIPTS REQUIRED!
========================================
