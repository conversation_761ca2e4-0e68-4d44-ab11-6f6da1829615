@echo off
echo.
echo ========================================
echo   Octopi Project Manager Installer
echo   Developed by <PERSON> (AntmanZA)
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Starting GUI installer...
    echo.
    powershell.exe -ExecutionPolicy Bypass -File "OctopiProjectManagerInstaller.ps1"
) else (
    echo This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
)
