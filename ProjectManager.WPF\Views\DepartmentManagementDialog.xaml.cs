using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using ProjectManager.WPF.Data;
using ProjectManager.WPF.Models;

namespace ProjectManager.WPF.Views
{
    public partial class DepartmentManagementDialog : Window
    {
        private readonly ProjectManagerDbContext _context;
        private List<DepartmentViewModel> _departments = new();

        public DepartmentManagementDialog(ProjectManagerDbContext context)
        {
            InitializeComponent();
            _context = context;
            LoadDepartments();
        }

        private async void LoadDepartments()
        {
            try
            {
                statusText.Text = "Loading departments...";
                
                var departments = await _context.Departments
                    .Include(d => d.Projects)
                    .OrderBy(d => d.Name)
                    .ToListAsync();

                _departments = departments.Select(d => new DepartmentViewModel
                {
                    Id = d.Id,
                    Name = d.Name,
                    Description = d.Description,
                    Color = d.Color,
                    IsActive = d.IsActive,
                    CreatedDate = d.CreatedDate,
                    ProjectCount = d.Projects.Count
                }).ToList();

                departmentDataGrid.ItemsSource = _departments;
                statusText.Text = $"Loaded {_departments.Count} departments";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading departments: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                statusText.Text = "Error loading departments";
            }
        }

        private async void BtnAddDepartment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new DepartmentFormDialog();
                if (dialog.ShowDialog() == true && dialog.Department != null)
                {
                    // Check for duplicate names
                    var existingDepartment = await _context.Departments
                        .FirstOrDefaultAsync(d => d.Name.ToLower() == dialog.Department.Name.ToLower());
                    
                    if (existingDepartment != null)
                    {
                        MessageBox.Show($"A department with the name '{dialog.Department.Name}' already exists.", 
                            "Duplicate Name", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    _context.Departments.Add(dialog.Department);
                    await _context.SaveChangesAsync();
                    
                    LoadDepartments();
                    statusText.Text = $"Department '{dialog.Department.Name}' added successfully";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding department: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                statusText.Text = "Error adding department";
            }
        }

        private async void BtnEditDepartment_Click(object sender, RoutedEventArgs e)
        {
            var selectedDepartment = GetSelectedDepartment();
            if (selectedDepartment == null) return;

            try
            {
                var department = await _context.Departments
                    .FirstOrDefaultAsync(d => d.Id == selectedDepartment.Id);
                
                if (department == null)
                {
                    MessageBox.Show("Department not found.", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var dialog = new DepartmentFormDialog(department);
                if (dialog.ShowDialog() == true)
                {
                    // Check for duplicate names (excluding current department)
                    var existingDepartment = await _context.Departments
                        .FirstOrDefaultAsync(d => d.Name.ToLower() == department.Name.ToLower() && d.Id != department.Id);
                    
                    if (existingDepartment != null)
                    {
                        MessageBox.Show($"A department with the name '{department.Name}' already exists.", 
                            "Duplicate Name", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    await _context.SaveChangesAsync();
                    
                    LoadDepartments();
                    statusText.Text = $"Department '{department.Name}' updated successfully";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error editing department: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                statusText.Text = "Error editing department";
            }
        }

        private async void BtnDeleteDepartment_Click(object sender, RoutedEventArgs e)
        {
            var selectedDepartment = GetSelectedDepartment();
            if (selectedDepartment == null) return;

            try
            {
                var department = await _context.Departments
                    .Include(d => d.Projects)
                    .FirstOrDefaultAsync(d => d.Id == selectedDepartment.Id);
                
                if (department == null)
                {
                    MessageBox.Show("Department not found.", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Check if department has projects
                if (department.Projects.Any())
                {
                    var result = MessageBox.Show(
                        $"Department '{department.Name}' has {department.Projects.Count} project(s) assigned to it.\n\n" +
                        "Deleting this department will remove the department assignment from these projects.\n\n" +
                        "Are you sure you want to continue?", 
                        "Confirm Delete", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Warning);
                    
                    if (result != MessageBoxResult.Yes)
                        return;
                }
                else
                {
                    var result = MessageBox.Show(
                        $"Are you sure you want to delete the department '{department.Name}'?", 
                        "Confirm Delete", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Question);
                    
                    if (result != MessageBoxResult.Yes)
                        return;
                }

                _context.Departments.Remove(department);
                await _context.SaveChangesAsync();
                
                LoadDepartments();
                statusText.Text = $"Department '{department.Name}' deleted successfully";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting department: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                statusText.Text = "Error deleting department";
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadDepartments();
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void DepartmentDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = departmentDataGrid.SelectedItem != null;
            btnEditDepartment.IsEnabled = hasSelection;
            btnDeleteDepartment.IsEnabled = hasSelection;
        }

        private DepartmentViewModel? GetSelectedDepartment()
        {
            return departmentDataGrid.SelectedItem as DepartmentViewModel;
        }
    }

    public class DepartmentViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Color { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ProjectCount { get; set; }
    }
}
