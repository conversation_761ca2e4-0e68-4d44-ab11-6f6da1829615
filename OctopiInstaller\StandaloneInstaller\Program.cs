using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using Microsoft.Win32;

namespace OctopiStandaloneInstaller
{
    class Program
    {
        private static string AppName = "Octopi Project Manager";
        private static string AppVersion = "1.1.0";
        private static string Publisher = "Conrad Cloete (AntmanZA)";
        private static string InstallPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "OctopiProjectManager");
        private static string AppExe = "ProjectManager.WPF.exe";

        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Check for uninstall argument
            if (args.Length > 0 && args[0].ToLower() == "/uninstall")
            {
                UninstallApplication();
                return;
            }

            try
            {
                ShowInstallDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Installation failed: {ex.Message}", 
                    "Installation Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void ShowInstallDialog()
        {
            var result = MessageBox.Show(
                $"Welcome to {AppName} v{AppVersion} Setup\n\n" +
                "This will install Octopi Project Manager with dynamic department management.\n\n" +
                "Features:\n" +
                "• Unlimited custom departments\n" +
                "• Professional project tracking\n" +
                "• Interactive Gantt charts\n" +
                "• CSV/Excel import and export\n" +
                "• Dark and light themes\n\n" +
                "Click OK to continue with installation.",
                $"Install {AppName}",
                MessageBoxButtons.OKCancel,
                MessageBoxIcon.Information);

            if (result == DialogResult.OK)
            {
                InstallApplication();
            }
        }

        private static void InstallApplication()
        {
            try
            {
                // Create installation directory
                if (!Directory.Exists(InstallPath))
                {
                    Directory.CreateDirectory(InstallPath);
                }

                // Extract embedded ZIP file
                ExtractEmbeddedFiles();

                // Create shortcuts
                CreateShortcuts();

                // Register application
                RegisterApplication();

                MessageBox.Show(
                    $"{AppName} has been installed successfully!\n\n" +
                    "You can now run the application from:\n" +
                    "• Desktop shortcut\n" +
                    "• Start Menu\n" +
                    "• Programs and Features\n\n" +
                    "The application includes dynamic department management - " +
                    "access it via Tools → Manage Departments.",
                    "Installation Complete",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Installation failed: {ex.Message}", 
                    "Installation Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void ExtractEmbeddedFiles()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "OctopiStandaloneInstaller.OctopiProjectManager.zip";

            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                {
                    throw new Exception("Could not find embedded application files.");
                }

                using (var archive = new ZipArchive(stream, ZipArchiveMode.Read))
                {
                    archive.ExtractToDirectory(InstallPath, true);
                }
            }
        }

        private static void CreateShortcuts()
        {
            try
            {
                // Create desktop shortcut
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var shortcutPath = Path.Combine(desktopPath, $"{AppName}.lnk");
                CreateShortcut(shortcutPath, Path.Combine(InstallPath, AppExe), InstallPath);

                // Create Start Menu folder and shortcut
                var startMenuPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), AppName);
                if (!Directory.Exists(startMenuPath))
                {
                    Directory.CreateDirectory(startMenuPath);
                }

                var startMenuShortcut = Path.Combine(startMenuPath, $"{AppName}.lnk");
                CreateShortcut(startMenuShortcut, Path.Combine(InstallPath, AppExe), InstallPath);

                // Create uninstaller shortcut in Start Menu
                var uninstallShortcut = Path.Combine(startMenuPath, "Uninstall Octopi Project Manager.lnk");
                var installerPath = Assembly.GetExecutingAssembly().Location;
                CreateShortcut(uninstallShortcut, installerPath, Path.GetDirectoryName(installerPath), "/uninstall");
            }
            catch (Exception ex)
            {
                // Continue installation even if shortcuts fail
                System.Diagnostics.Debug.WriteLine($"Shortcut creation failed: {ex.Message}");
            }
        }

        private static void CreateShortcut(string shortcutPath, string targetPath, string workingDirectory, string arguments = "")
        {
            try
            {
                var shell = Activator.CreateInstance(Type.GetTypeFromProgID("WScript.Shell"));
                var shortcut = shell.GetType().InvokeMember("CreateShortcut", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shell, new object[] { shortcutPath });

                shortcut.GetType().InvokeMember("TargetPath", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { targetPath });
                shortcut.GetType().InvokeMember("WorkingDirectory", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { workingDirectory });
                shortcut.GetType().InvokeMember("Description", 
                    System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { $"{AppName} - Project Tracker" });
                
                if (!string.IsNullOrEmpty(arguments))
                {
                    shortcut.GetType().InvokeMember("Arguments", 
                        System.Reflection.BindingFlags.SetProperty, null, shortcut, new object[] { arguments });
                }

                shortcut.GetType().InvokeMember("Save", 
                    System.Reflection.BindingFlags.InvokeMethod, null, shortcut, null);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create shortcut {shortcutPath}: {ex.Message}");
            }
        }

        private static void RegisterApplication()
        {
            try
            {
                var uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                using (var key = Registry.LocalMachine.CreateSubKey(uninstallKey))
                {
                    key.SetValue("DisplayName", AppName);
                    key.SetValue("DisplayVersion", AppVersion);
                    key.SetValue("Publisher", Publisher);
                    key.SetValue("InstallLocation", InstallPath);
                    key.SetValue("DisplayIcon", Path.Combine(InstallPath, AppExe));
                    key.SetValue("UninstallString", $"\"{Assembly.GetExecutingAssembly().Location}\" /uninstall");
                    key.SetValue("EstimatedSize", 160000, RegistryValueKind.DWord);
                    key.SetValue("NoModify", 1, RegistryValueKind.DWord);
                    key.SetValue("NoRepair", 1, RegistryValueKind.DWord);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to register application: {ex.Message}");
            }
        }

        private static void UninstallApplication()
        {
            try
            {
                var result = MessageBox.Show(
                    $"Are you sure you want to uninstall {AppName}?\n\n" +
                    "This will remove:\n" +
                    "• Application files\n" +
                    "• Desktop and Start Menu shortcuts\n" +
                    "• Registry entries\n\n" +
                    "Your project data will be preserved.",
                    "Uninstall Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                // Remove shortcuts
                var desktopShortcut = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), $"{AppName}.lnk");
                if (File.Exists(desktopShortcut))
                    File.Delete(desktopShortcut);

                var startMenuPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonPrograms), AppName);
                if (Directory.Exists(startMenuPath))
                    Directory.Delete(startMenuPath, true);

                // Remove registry entry
                var uninstallKey = $@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{AppName}";
                Registry.LocalMachine.DeleteSubKey(uninstallKey, false);

                // Remove application files (preserve databases)
                if (Directory.Exists(InstallPath))
                {
                    var filesToRemove = Directory.GetFiles(InstallPath, "*.*", SearchOption.AllDirectories)
                        .Where(f => !f.EndsWith(".db") && !f.EndsWith(".db-journal") && !f.EndsWith(".db-wal"))
                        .ToArray();

                    foreach (var file in filesToRemove)
                    {
                        try { File.Delete(file); } catch { }
                    }

                    // Remove directory if empty
                    try
                    {
                        if (!Directory.EnumerateFileSystemEntries(InstallPath).Any())
                            Directory.Delete(InstallPath);
                    }
                    catch { }
                }

                MessageBox.Show(
                    $"{AppName} has been uninstalled successfully!\n\n" +
                    "Your project data has been preserved.",
                    "Uninstall Complete",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uninstall failed: {ex.Message}", 
                    "Uninstall Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
